#!/usr/bin/env python3
"""
Simple endpoint testing script for RingCX API.
Tests various endpoint variations to find working ones.
"""

import requests
import json
from rc_auth import get_ringcx_access_token_from_rc_credentials, extract_access_token

def get_ringcx_access_token():
    """Get RingCX access token."""
    try:
        response = get_ringcx_access_token_from_rc_credentials()
        return extract_access_token(response)
    except Exception as e:
        raise RuntimeError(f"Failed to get RingCX access token: {str(e)}")

def test_endpoint(endpoint, method="GET", access_token=None):
    """
    Test a single endpoint.
    
    Args:
        endpoint (str): API endpoint to test
        method (str): HTTP method (GET, POST, etc.)
        access_token (str): RingCX access token
    
    Returns:
        dict: Test result
    """
    if not access_token:
        access_token = get_ringcx_access_token()
    
    base_url = "https://ringcx.ringcentral.com/voice"
    url = f"{base_url}{endpoint}"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json"
    }
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": response.status_code,
            "status_text": response.reason,
            "success": response.status_code == 200
        }
        
        if response.status_code == 200:
            try:
                result["response"] = response.json()
                result["response_preview"] = str(response.text)[:200] + "..."
            except:
                result["response_text"] = response.text[:200] + "..."
        else:
            result["error_text"] = response.text[:300] + "..."
        
        return result
        
    except Exception as e:
        return {
            "endpoint": endpoint,
            "method": method,
            "error": str(e),
            "success": False
        }

def test_multiple_endpoints():
    """Test multiple endpoint variations."""
    
    print("RingCX API Endpoint Testing")
    print("=" * 50)
    
    # Get access token
    try:
        access_token = get_ringcx_access_token()
        print(f"✓ Access token obtained: {access_token[:50]}...")
    except Exception as e:
        print(f"✗ Failed to get access token: {e}")
        return
    
    # Test endpoints
    endpoints_to_test = [
        # auxStates variations
        ("/api/v1/admin/accounts/********/auxStates", "GET"),
        ("/api/v1/admin/accounts/********/auxStates?activeOnly=true", "GET"),
        ("/api/v1/admin/accounts/********/auxStates/", "GET"),
        ("/api/v1/admin/accounts/********/auxStates/?activeOnly=true", "GET"),
        
        # Account variations
        ("/api/v1/admin/accounts/********", "GET"),
        ("/api/v1/admin/accounts/********/", "GET"),
        ("/api/v1/accounts/********", "GET"),
        
        # Agent variations
        ("/api/v1/admin/accounts/********/agents", "GET"),
        ("/api/v1/admin/accounts/********/users", "GET"),
        
        # Campaign variations
        ("/api/v1/admin/accounts/********/campaigns", "GET"),
        
        # Reports variations
        ("/api/v1/admin/accounts/********/reports", "GET"),
        ("/api/v1/admin/accounts/********/reportsStreaming", "GET"),
        ("/api/v1/admin/accounts/********/reportsStreaming", "POST"),
        
        # Root endpoints
        ("/api/v1/admin", "GET"),
        ("/api/v1", "GET"),
        ("/api", "GET"),
    ]
    
    print(f"\nTesting {len(endpoints_to_test)} endpoints...")
    print("=" * 50)
    
    results = []
    working_endpoints = []
    
    for endpoint, method in endpoints_to_test:
        print(f"\n{method} {endpoint}")
        result = test_endpoint(endpoint, method, access_token)
        results.append(result)
        
        if result.get("success"):
            print(f"  ✓ SUCCESS: {result['status_code']} {result['status_text']}")
            working_endpoints.append((endpoint, method))
            if "response_preview" in result:
                print(f"  Response: {result['response_preview']}")
        elif result.get("status_code") == 404:
            print(f"  ? NOT FOUND: {result['status_code']} {result['status_text']}")
        elif result.get("status_code") == 403:
            print(f"  ✗ FORBIDDEN: {result['status_code']} {result['status_text']}")
        elif result.get("status_code") == 405:
            print(f"  ⚠ METHOD NOT ALLOWED: {result['status_code']} {result['status_text']}")
        else:
            print(f"  ✗ ERROR: {result.get('status_code', 'N/A')} {result.get('status_text', result.get('error', 'Unknown'))}")
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    if working_endpoints:
        print(f"✓ Working endpoints found: {len(working_endpoints)}")
        for endpoint, method in working_endpoints:
            print(f"  {method} {endpoint}")
    else:
        print("✗ No working endpoints found")
    
    # Categorize errors
    forbidden_count = sum(1 for r in results if r.get("status_code") == 403)
    not_found_count = sum(1 for r in results if r.get("status_code") == 404)
    method_not_allowed_count = sum(1 for r in results if r.get("status_code") == 405)
    
    print(f"\nError breakdown:")
    print(f"  403 Forbidden: {forbidden_count}")
    print(f"  404 Not Found: {not_found_count}")
    print(f"  405 Method Not Allowed: {method_not_allowed_count}")
    
    if forbidden_count > 0:
        print("\nNote: 403 Forbidden suggests the endpoint exists but lacks permissions")
    if not_found_count > 0:
        print("Note: 404 Not Found suggests the endpoint doesn't exist or wrong path")
    
    return results

if __name__ == "__main__":
    test_multiple_endpoints()
