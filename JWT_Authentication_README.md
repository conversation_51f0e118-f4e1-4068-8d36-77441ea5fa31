# RingCX JWT Authentication

This guide explains how to connect to the RingCX API using JWT (JSON Web Token) authentication.

## Overview

JWT authentication provides a secure way to authenticate with RingCentral and RingCX APIs without storing long-lived access tokens. The process involves:

1. Creating a JWT token signed with your private key
2. Exchanging the JWT for a RingCentral access token
3. Using the RingCentral access token to get a RingCX access token

## Prerequisites

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Required packages:
- `PyJWT>=2.8.0` - JWT token creation and validation
- `cryptography>=3.4.0` - Cryptographic operations for JWT signing
- `requests>=2.25.0` - HTTP requests

### 2. RingCentral Application Setup

1. Go to [RingCentral Developer Portal](https://developers.ringcentral.com)
2. Create a new application or use an existing one
3. Note your `Client ID` and `Client Secret`
4. Generate a public/private key pair for JWT signing
5. Upload the public key to your RingCentral application

### 3. Generate Key Pair

You can generate a key pair using OpenSSL:

```bash
# Generate private key
openssl genpkey -algorithm RSA -out private_key.pem -pkcs8 -pass pass:your_password

# Generate public key
openssl rsa -pubout -in private_key.pem -out public_key.pem
```

## Configuration

### JWT Credentials File

Create `JWT_CREDENTIALS.json` with your credentials:

```json
{
    "client_id": "your_ringcentral_client_id",
    "client_secret": "your_ringcentral_client_secret",
    "jwt_private_key": "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----",
    "server_url": "https://platform.ringcentral.com"
}
```

**Security Note:** Keep this file secure and never commit it to version control.

## Functions

### Core JWT Functions

#### `create_jwt_token(client_id, client_secret, jwt_private_key, server_url=None)`

Creates a JWT token for RingCentral authentication.

**Parameters:**
- `client_id` (str): RingCentral application client ID
- `client_secret` (str): RingCentral application client secret  
- `jwt_private_key` (str): Private key for JWT signing (PEM format)
- `server_url` (str, optional): RingCentral server URL

**Returns:** JWT token string

#### `get_ringcentral_access_token_with_jwt(client_id, client_secret, jwt_private_key, server_url=None)`

Gets RingCentral access token using JWT authentication.

**Parameters:** Same as `create_jwt_token`

**Returns:** Dictionary with access token and metadata

#### `get_ringcx_access_token_with_jwt(jwt_credentials=None, credentials_file="JWT_CREDENTIALS.json")`

Gets RingCX access token using JWT authentication (complete flow).

**Parameters:**
- `jwt_credentials` (dict, optional): JWT credentials dictionary
- `credentials_file` (str): Path to credentials JSON file

**Returns:** Dictionary with RingCX access token

### Utility Functions

#### `read_jwt_credentials_from_file(credentials_file="JWT_CREDENTIALS.json")`

Reads and validates JWT credentials from a JSON file.

#### `extract_access_token(auth_response)`

Extracts access token from authentication response.

## Usage Examples

### Basic JWT Authentication

```python
from getRingCX import get_ringcx_access_token_with_jwt, extract_access_token

try:
    # Authenticate using JWT credentials file
    auth_response = get_ringcx_access_token_with_jwt()
    
    # Extract the access token
    access_token = extract_access_token(auth_response)
    
    print(f"Successfully authenticated: {access_token[:50]}...")
    
    # Use the access token for API calls
    headers = {"Authorization": f"Bearer {access_token}"}
    
except RuntimeError as e:
    print(f"Authentication failed: {e}")
```

### Custom Credentials

```python
from getRingCX import get_ringcx_access_token_with_jwt

credentials = {
    "client_id": "your_client_id",
    "client_secret": "your_client_secret", 
    "jwt_private_key": "your_private_key",
    "server_url": "https://platform.ringcentral.com"
}

try:
    auth_response = get_ringcx_access_token_with_jwt(credentials)
    # ... handle response
except RuntimeError as e:
    print(f"Error: {e}")
```

### Step-by-Step Authentication

```python
from getRingCX import (
    create_jwt_token,
    get_ringcentral_access_token_with_jwt,
    get_ringcx_access_token
)

# Step 1: Create JWT token
jwt_token = create_jwt_token(client_id, client_secret, private_key)

# Step 2: Get RingCentral access token
rc_response = get_ringcentral_access_token_with_jwt(client_id, client_secret, private_key)
rc_access_token = rc_response["access_token"]

# Step 3: Get RingCX access token
ringcx_response = get_ringcx_access_token(rc_access_token)
```

## Testing

### Run JWT Tests

```bash
# Basic test
python test_jwt_auth.py

# Create sample credentials file
python test_jwt_auth.py --create-sample

# Show setup instructions
python test_jwt_auth.py --help
```

### Test Output

Successful authentication will show:
```
✓ JWT credentials file found and parsed
✓ JWT token created successfully
✓ RingCentral access token obtained
✓ RingCX access token obtained
✓ All JWT authentication tests passed!
```

## Authentication Flow

```
1. JWT Credentials → 2. Create JWT Token → 3. RingCentral API
                                              ↓
6. RingCX API ← 5. RingCX Access Token ← 4. RC Access Token
```

1. **JWT Credentials**: Client ID, secret, and private key
2. **Create JWT Token**: Sign JWT with private key
3. **RingCentral API**: Exchange JWT for access token
4. **RC Access Token**: RingCentral access token
5. **RingCX Access Token**: Exchange RC token for RingCX token
6. **RingCX API**: Use RingCX token for API calls

## Error Handling

Common errors and solutions:

### JWT Creation Errors
- **Invalid private key**: Check key format (PEM) and encoding
- **Missing credentials**: Verify all required fields in credentials file

### RingCentral Authentication Errors
- **Invalid client credentials**: Check client ID and secret
- **JWT signature verification failed**: Ensure public key is uploaded to RingCentral
- **JWT expired**: JWT tokens expire after 5 minutes - regenerate if needed

### RingCX Authentication Errors
- **Invalid RC access token**: Check RingCentral authentication was successful
- **Network errors**: Verify connectivity to RingCX endpoints

## Security Best Practices

1. **Protect Private Keys**: Store private keys securely, never in code
2. **Use Environment Variables**: Consider using environment variables for sensitive data
3. **Rotate Keys**: Regularly rotate your key pairs
4. **Limit Token Scope**: Use minimum required permissions
5. **Monitor Usage**: Track authentication attempts and failures

## Troubleshooting

### Debug Mode

Enable debug output by modifying the functions to print intermediate values:

```python
# Add debug prints to see JWT payload
print(f"JWT payload: {payload}")
print(f"JWT token: {jwt_token}")
```

### Common Issues

1. **"JWT signature verification failed"**
   - Ensure public key is correctly uploaded to RingCentral
   - Check private key format and encoding

2. **"Invalid client credentials"**
   - Verify client ID and secret are correct
   - Check if application is active in RingCentral

3. **"Token expired"**
   - JWT tokens expire quickly - regenerate for each request
   - Check system clock synchronization

## File Structure

```
Call Analysis/
├── getRingCX.py                    # Main authentication module
├── JWT_CREDENTIALS.json            # JWT credentials (keep secure)
├── test_jwt_auth.py               # JWT testing script
├── requirements.txt               # Python dependencies
├── JWT_Authentication_README.md   # This documentation
└── other files...
```
