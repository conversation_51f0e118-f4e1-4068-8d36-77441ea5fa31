import requests
import time
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocDocument, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from datetime import datetime

def analyze_audio(audio_url, agent_name=None, api_key="1f5263b703144fe8b86599ededb44525"):
    """
    Analyze audio using AssemblyAI API and return summary, sentiment score, and transcript with speaker labels.

    Args:
        audio_url (str): URL of the audio file to analyze
        agent_name (str, optional): Name of the agent to try to match with a speaker
        api_key (str): AssemblyAI API key

    Returns:
        tuple: (summary, avg_sentiment_score, transcript_data)
        transcript_data is a dict with:
        - 'agent_speaker': speaker label for the agent (if identified)
        - 'customer_speaker': speaker label for the customer (if identified)
        - 'utterances': list of dicts with 'speaker', 'text', 'start', 'end'
    """
    base_url = "https://api.assemblyai.com"
    
    headers = {
        "authorization": api_key,
    }
    
    data = {
        "audio_url": audio_url,
        "speaker_labels": True,
        "speakers_expected": 2,
        "sentiment_analysis": True,
        "summarization": True,
        "summary_model": "informative",
        "summary_type": "bullets"
    }
    
    # Submit transcription request
    url = base_url + "/v2/transcript"
    response = requests.post(url, json=data, headers=headers)
    transcript_id = response.json()['id']
    polling_endpoint = base_url + "/v2/transcript/" + transcript_id
    
    # Poll for results
    while True:
        transcription_result = requests.get(polling_endpoint, headers=headers).json()
        
        if transcription_result['status'] == 'completed':
            # Get summary
            summary = transcription_result['summary']

            # Calculate average sentiment score
            sentiments = transcription_result['sentiment_analysis_results']
            sentiment_values = []

            for sentiment in sentiments:
                if sentiment['sentiment'] == 'POSITIVE':
                    value = sentiment['confidence']
                elif sentiment['sentiment'] == 'NEGATIVE':
                    value = -sentiment['confidence']
                else:  # NEUTRAL
                    value = 0
                sentiment_values.append(value)

            # Calculate raw average sentiment score
            raw_avg_sentiment = np.mean(sentiment_values) if sentiment_values else 0

            # Amplify the sentiment score to make differences more noticeable
            # This will expand the typical range of -0.3 to 0.3 to a range of -1.0 to 1.0
            amplified_sentiment = np.tanh(raw_avg_sentiment * 5)

            # Process transcript with speaker labels
            transcript_data = _process_transcript_with_speakers(
                transcription_result.get('utterances', []),
                agent_name
            )

            return summary, amplified_sentiment, transcript_data
            
        elif transcription_result['status'] == 'error':
            raise RuntimeError(f"Transcription failed: {transcription_result['error']}")
        
        else:
            time.sleep(3)

def _process_transcript_with_speakers(utterances, agent_name=None):
    """
    Process transcript utterances to identify speakers and format transcript data.

    Args:
        utterances (list): List of utterance objects from AssemblyAI
        agent_name (str, optional): Name of the agent to try to match

    Returns:
        dict: Processed transcript data with speaker identification
    """
    if not utterances:
        return {
            'agent_speaker': None,
            'customer_speaker': None,
            'utterances': []
        }

    # Extract utterances with speaker info
    processed_utterances = []
    speaker_texts = {}  # speaker -> list of texts

    for utterance in utterances:
        speaker = utterance.get('speaker', 'Unknown')
        text = utterance.get('text', '')
        start = utterance.get('start', 0)
        end = utterance.get('end', 0)

        processed_utterances.append({
            'speaker': speaker,
            'text': text,
            'start': start,
            'end': end
        })

        # Collect texts by speaker for analysis
        if speaker not in speaker_texts:
            speaker_texts[speaker] = []
        speaker_texts[speaker].append(text.lower())

    # Try to identify which speaker is the agent
    agent_speaker, customer_speaker = _identify_agent_speaker(speaker_texts, agent_name)

    return {
        'agent_speaker': agent_speaker,
        'customer_speaker': customer_speaker,
        'utterances': processed_utterances
    }

def _identify_agent_speaker(speaker_texts, agent_name=None):
    """
    Try to identify which speaker is the agent based on common agent phrases and patterns.

    Args:
        speaker_texts (dict): Dictionary mapping speaker labels to lists of their texts
        agent_name (str, optional): Agent name to help with identification

    Returns:
        tuple: (agent_speaker, customer_speaker) or (None, None) if can't determine
    """
    # Common phrases agents use
    agent_phrases = [
        'thank you for calling',
        'how can i help',
        'how may i assist',
        'my name is',
        'this is',
        'company policy',
        'let me check',
        'i can help you with',
        'i understand',
        'i apologize',
        'is there anything else',
        'have a great day',
        'thank you for your business',
        'let me transfer you',
        'one moment please'
    ]

    speaker_scores = {}

    for speaker, texts in speaker_texts.items():
        score = 0
        all_text = ' '.join(texts)

        # Check for agent phrases
        for phrase in agent_phrases:
            if phrase in all_text:
                score += 1

        # Check if agent name appears in the text (agent introducing themselves)
        if agent_name:
            agent_name_lower = agent_name.lower()
            # Check for name mentions
            if agent_name_lower in all_text:
                score += 2
            # Check for first name only
            first_name = agent_name_lower.split()[0] if ' ' in agent_name_lower else agent_name_lower
            if first_name in all_text and len(first_name) > 2:
                score += 1

        speaker_scores[speaker] = score

    # Find the speaker with the highest agent score
    if speaker_scores:
        sorted_speakers = sorted(speaker_scores.items(), key=lambda x: x[1], reverse=True)

        # If there's a clear winner (higher score than others)
        if len(sorted_speakers) >= 2 and sorted_speakers[0][1] > sorted_speakers[1][1]:
            agent_speaker = sorted_speakers[0][0]
            customer_speaker = sorted_speakers[1][0]
            return agent_speaker, customer_speaker
        elif len(sorted_speakers) == 1:
            # Only one speaker, assume they're the agent
            return sorted_speakers[0][0], None

    # If we can't determine, return the speakers as-is
    speakers = list(speaker_texts.keys())
    if len(speakers) >= 2:
        return speakers[0], speakers[1]
    elif len(speakers) == 1:
        return speakers[0], None

    return None, None

def extract_call_data(csv_file_path):
    """
    Extract agent name and recording URL from a CSV file.
    
    Args:
        csv_file_path (str): Path to the CSV file
        
    Returns:
        list: List of tuples containing (agent_name, recording_url)
    """
    # Read CSV file
    df = pd.read_csv(csv_file_path)
    
    # Extract agent names and recording URLs, filtering out NaN values
    call_data = []
    for _, row in df.iterrows():
        agent_name = row["Agent Full Name"]
        recording_url = row["Recording URL"]
        # Skip rows where either agent_name or recording_url is NaN
        if pd.notna(agent_name) and pd.notna(recording_url):
            call_data.append((agent_name, recording_url))
    
    return call_data

def create_call_selection_ui(call_data):
    """
    Create a UI to select which calls to process.
    
    Args:
        call_data (list): List of tuples containing (agent_name, recording_url)
        
    Returns:
        list: List of selected call data tuples
    """
    selected_calls = []
    selection_complete = threading.Event()
    
    def on_process_selected():
        for i, var in enumerate(check_vars):
            if var.get():
                selected_calls.append(call_data[i])
        root.destroy()
        selection_complete.set()
    
    # Create the main window
    root = tk.Tk()
    root.title("Call Data Selection")
    root.geometry("800x400")
    
    # Create a frame for the call data
    frame = ttk.Frame(root)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Create a scrollable area
    canvas = tk.Canvas(frame)
    scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # Add headers
    ttk.Label(scrollable_frame, text="Select", width=10).grid(row=0, column=0, padx=5, pady=5)
    ttk.Label(scrollable_frame, text="Agent Name", width=20).grid(row=0, column=1, padx=5, pady=5)
    ttk.Label(scrollable_frame, text="Recording URL", width=60).grid(row=0, column=2, padx=5, pady=5)
    
    # Add call data with checkboxes
    check_vars = []
    for i, (agent_name, recording_url) in enumerate(call_data):
        var = tk.BooleanVar()
        check_vars.append(var)
        ttk.Checkbutton(scrollable_frame, variable=var).grid(row=i+1, column=0, padx=5, pady=2)
        ttk.Label(scrollable_frame, text=agent_name).grid(row=i+1, column=1, padx=5, pady=2, sticky="w")
        url_label = ttk.Label(scrollable_frame, text=recording_url[:50] + "...")
        url_label.grid(row=i+1, column=2, padx=5, pady=2, sticky="w")
    
    # Add buttons
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=10)
    
    ttk.Button(button_frame, text="Select All", command=lambda: [var.set(True) for var in check_vars]).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Deselect All", command=lambda: [var.set(False) for var in check_vars]).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Process Selected", command=on_process_selected).pack(side=tk.RIGHT, padx=5)
    
    root.mainloop()
    selection_complete.wait()
    return selected_calls

def select_csv_file():
    """
    Open a file dialog to select a CSV file.
    
    Returns:
        str: Path to the selected CSV file or None if canceled
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    file_path = filedialog.askopenfilename(
        title="Select CSV File",
        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
    )
    root.destroy()
    return file_path if file_path else None

def generate_pdf_report(results, output_path=None):
    """
    Generate a PDF report with call analysis results including transcripts.

    Args:
        results (list): List of tuples containing (agent_name, summary, sentiment_score, transcript_data)
        output_path (str, optional): Path to save the PDF. If None, generates a default name.

    Returns:
        str: Path to the generated PDF file
    """
    if output_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"call_analysis_report_{timestamp}.pdf"
    
    # Create the PDF document
    doc = SimpleDocTemplate(output_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Create custom styles
    title_style = styles["Heading1"]
    heading_style = styles["Heading2"]
    normal_style = styles["Normal"]
    
    # Create a list to hold the flowables
    elements = []
    
    # Add title
    elements.append(Paragraph("Call Analysis Report", title_style))
    elements.append(Spacer(1, 12))
    elements.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))
    elements.append(Spacer(1, 24))
    
    # Add each call analysis
    for i, result in enumerate(results):
        # Handle both old format (3 items) and new format (4 items) for backward compatibility
        if len(result) == 4:
            agent_name, summary, sentiment_score, transcript_data = result
        else:
            agent_name, summary, sentiment_score = result
            transcript_data = None
        # Add agent name
        elements.append(Paragraph(f"Call {i+1}: {agent_name}", heading_style))
        elements.append(Spacer(1, 6))
        
        # Add sentiment score with color coding and interpretation
        sentiment_text = f"Sentiment Score: {sentiment_score:.2f}"
        
        # More granular sentiment interpretation
        if sentiment_score > 0.6:
            sentiment_color = colors.green
            sentiment_text += " (Very Positive)"
        elif sentiment_score > 0.2:
            sentiment_color = colors.forestgreen
            sentiment_text += " (Positive)"
        elif sentiment_score > -0.2:
            sentiment_color = colors.black
            sentiment_text += " (Neutral)"
        elif sentiment_score > -0.6:
            sentiment_color = colors.orangered
            sentiment_text += " (Negative)"
        else:
            sentiment_color = colors.red
            sentiment_text += " (Very Negative)"
            
        sentiment_style = ParagraphStyle(
            "sentiment", 
            parent=normal_style, 
            textColor=sentiment_color,
            fontName="Helvetica-Bold"
        )
        elements.append(Paragraph(sentiment_text, sentiment_style))
        elements.append(Spacer(1, 6))
        
        # Add summary
        elements.append(Paragraph("Summary:", styles["Heading3"]))
        # Split summary by bullet points if it's in that format
        if isinstance(summary, list):
            for bullet in summary:
                elements.append(Paragraph(f"• {bullet}", normal_style))
                elements.append(Spacer(1, 3))
        else:
            # Handle string format (might be bullet-separated with newlines)
            summary_parts = summary.split("\n")
            for part in summary_parts:
                if part.strip():
                    elements.append(Paragraph(part.strip(), normal_style))
                    elements.append(Spacer(1, 3))

        # Add transcript section if available
        if transcript_data and transcript_data.get('utterances'):
            elements.append(Spacer(1, 12))
            elements.append(Paragraph("Full Transcript:", styles["Heading3"]))
            elements.append(Spacer(1, 6))

            # Get speaker labels for display
            agent_speaker = transcript_data.get('agent_speaker')
            customer_speaker = transcript_data.get('customer_speaker')

            for utterance in transcript_data['utterances']:
                speaker = utterance['speaker']
                text = utterance['text']

                # Determine display name for speaker
                if speaker == agent_speaker:
                    display_speaker = f"{agent_name} (Agent)"
                elif speaker == customer_speaker:
                    display_speaker = "Customer"
                else:
                    display_speaker = f"Speaker {speaker}"

                # Format the utterance
                speaker_style = ParagraphStyle(
                    "speaker",
                    parent=normal_style,
                    fontName="Helvetica-Bold",
                    textColor=colors.blue if speaker == agent_speaker else colors.darkgreen
                )

                elements.append(Paragraph(f"{display_speaker}:", speaker_style))
                elements.append(Paragraph(text, normal_style))
                elements.append(Spacer(1, 6))

        elements.append(Spacer(1, 24))
    
    # Build the PDF
    doc.build(elements)
    return output_path

def process_calls_with_progress(selected_calls):
    """
    Process selected calls with a progress bar UI.

    Args:
        selected_calls (list): List of tuples containing (agent_name, recording_url)

    Returns:
        list: List of results (agent_name, summary, sentiment_score, transcript_data)
    """
    results = []
    error_messages = []
    
    # Create a new Tk window for the progress bar
    root = tk.Tk()
    root.title("Processing Calls")
    root.geometry("400x150")
    
    # Add labels and progress bar
    tk.Label(root, text="Analyzing call recordings...", font=("Arial", 12)).pack(pady=10)
    
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(root, variable=progress_var, length=350, mode="determinate")
    progress_bar.pack(pady=10)
    
    status_var = tk.StringVar(value="Starting...")
    status_label = tk.Label(root, textvariable=status_var, font=("Arial", 10))
    status_label.pack(pady=5)
    
    total_calls = len(selected_calls)
    
    # Process each call one by one
    for i, (agent_name, recording_url) in enumerate(selected_calls):
        # Update progress bar
        progress_var.set((i / total_calls) * 100)
        status_var.set(f"Processing call {i+1} of {total_calls}: {agent_name}")
        root.update()
        
        try:
            # Process the call
            summary, sentiment_score, transcript_data = analyze_audio(recording_url, agent_name)
            results.append((agent_name, summary, sentiment_score, transcript_data))
        except Exception as e:
            error_messages.append(f"Failed to process call for {agent_name}: {str(e)}")
    
    # Complete the progress
    progress_var.set(100)
    status_var.set("Processing complete!")
    root.update()
    
    # Close the window after a short delay
    root.after(1000, root.destroy)
    root.mainloop()
    
    # Show any error messages
    if error_messages:
        error_text = "\n\n".join(error_messages)
        messagebox.showerror("Errors Occurred", f"Some calls could not be processed:\n\n{error_text}")
    
    return results

# Example usage
if __name__ == "__main__":
    # Let user select CSV file
    csv_path = select_csv_file()
    
    if not csv_path:
        print("No CSV file selected. Exiting.")
        exit()
    
    call_data = extract_call_data(csv_path)
    
    if not call_data:
        print("No valid call data found in the CSV file.")
        exit()
    
    selected_calls = create_call_selection_ui(call_data)
    
    if not selected_calls:
        print("No calls selected for processing. Exiting.")
        exit()
    
    # Process selected calls with progress bar
    results = process_calls_with_progress(selected_calls)
    
    # Generate PDF report
    if results:
        pdf_path = generate_pdf_report(results)
        messagebox.showinfo("Success", f"PDF report generated: {pdf_path}")
        print(f"PDF report generated: {pdf_path}")
