import requests
import time
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from datetime import datetime
import sqlite3
import os

def analyze_audio(audio_url, agent_name=None):
    """
    Analyze audio using AssemblyAI API and ChatGPT API for enhanced analysis.

    Args:
        audio_url (str): URL of the audio file to analyze
        agent_name (str, optional): Name of the agent to try to match with a speaker

    Returns:
        tuple: (summary, avg_sentiment_score, chatgpt_summary)
        - summary: Original AssemblyAI summary
        - avg_sentiment_score: Calculated sentiment score
        - chatgpt_summary: ChatGPT analysis of transcript (automatically loads API key from file)
    """
    # Get AssemblyAI API key from file
    assemblyai_api_key = get_assemblyai_api_key()

    base_url = "https://api.assemblyai.com"

    headers = {
        "authorization": assemblyai_api_key,
    }
    
    data = {
        "audio_url": audio_url,
        "speaker_labels": True,
        "speakers_expected": 2,
        "sentiment_analysis": True,
        "summarization": True,
        "summary_model": "informative",
        "summary_type": "bullets"
    }
    
    # Submit transcription request
    url = base_url + "/v2/transcript"
    response = requests.post(url, json=data, headers=headers)
    transcript_id = response.json()['id']
    polling_endpoint = base_url + "/v2/transcript/" + transcript_id
    
    # Poll for results
    while True:
        transcription_result = requests.get(polling_endpoint, headers=headers).json()
        
        if transcription_result['status'] == 'completed':
            # Get summary
            summary = transcription_result['summary']

            # Calculate average sentiment score
            sentiments = transcription_result['sentiment_analysis_results']
            sentiment_values = []

            for sentiment in sentiments:
                if sentiment['sentiment'] == 'POSITIVE':
                    value = sentiment['confidence']
                elif sentiment['sentiment'] == 'NEGATIVE':
                    value = -sentiment['confidence']
                else:  # NEUTRAL
                    value = 0
                sentiment_values.append(value)

            # Calculate raw average sentiment score
            raw_avg_sentiment = np.mean(sentiment_values) if sentiment_values else 0

            # Amplify the sentiment score to make differences more noticeable
            # This will expand the typical range of -0.3 to 0.3 to a range of -1.0 to 1.0
            amplified_sentiment = np.tanh(raw_avg_sentiment * 10)

            # Process transcript with speaker labels
            transcript_data = _process_transcript_with_speakers(
                transcription_result.get('utterances', []),
                agent_name
            )

            # Get ChatGPT analysis if transcript is available
            chatgpt_summary = None
            if transcript_data.get('utterances'):
                try:
                    chatgpt_summary = _get_chatgpt_analysis(transcript_data, agent_name)
                except Exception as e:
                    print(f"Warning: ChatGPT analysis failed: {str(e)}")
                    chatgpt_summary = "ChatGPT analysis unavailable due to API error."

            return summary, amplified_sentiment, chatgpt_summary
            
        elif transcription_result['status'] == 'error':
            raise RuntimeError(f"Transcription failed: {transcription_result['error']}")
        
        else:
            time.sleep(3)

def _process_transcript_with_speakers(utterances, agent_name=None):
    """
    Process transcript utterances to identify speakers and format transcript data.

    Args:
        utterances (list): List of utterance objects from AssemblyAI
        agent_name (str, optional): Name of the agent to try to match

    Returns:
        dict: Processed transcript data with speaker identification
    """
    if not utterances:
        return {
            'agent_speaker': None,
            'customer_speaker': None,
            'utterances': []
        }

    # Extract utterances with speaker info
    processed_utterances = []
    speaker_texts = {}  # speaker -> list of texts

    for utterance in utterances:
        speaker = utterance.get('speaker', 'Unknown')
        text = utterance.get('text', '')
        start = utterance.get('start', 0)
        end = utterance.get('end', 0)

        processed_utterances.append({
            'speaker': speaker,
            'text': text,
            'start': start,
            'end': end
        })

        # Collect texts by speaker for analysis
        if speaker not in speaker_texts:
            speaker_texts[speaker] = []
        speaker_texts[speaker].append(text.lower())

    # Try to identify which speaker is the agent
    agent_speaker, customer_speaker = _identify_agent_speaker(speaker_texts, agent_name)

    return {
        'agent_speaker': agent_speaker,
        'customer_speaker': customer_speaker,
        'utterances': processed_utterances
    }

def _identify_agent_speaker(speaker_texts, agent_name=None):
    """
    Try to identify which speaker is the agent based on common agent phrases and patterns.

    Args:
        speaker_texts (dict): Dictionary mapping speaker labels to lists of their texts
        agent_name (str, optional): Agent name to help with identification

    Returns:
        tuple: (agent_speaker, customer_speaker) or (None, None) if can't determine
    """
    # Common phrases agents use
    agent_phrases = [
        'thank you for calling',
        'how can i help',
        'how may i assist',
        'my name is',
        'this is',
        'company policy',
        'let me check',
        'i can help you with',
        'i understand',
        'i apologize',
        'is there anything else',
        'have a great day',
        'thank you for your business',
        'let me transfer you',
        'one moment please'
    ]

    speaker_scores = {}

    for speaker, texts in speaker_texts.items():
        score = 0
        all_text = ' '.join(texts)

        # Check for agent phrases
        for phrase in agent_phrases:
            if phrase in all_text:
                score += 1

        # Check if agent name appears in the text (agent introducing themselves)
        if agent_name:
            agent_name_lower = agent_name.lower()
            # Check for name mentions
            if agent_name_lower in all_text:
                score += 2
            # Check for first name only
            first_name = agent_name_lower.split()[0] if ' ' in agent_name_lower else agent_name_lower
            if first_name in all_text and len(first_name) > 2:
                score += 1

        speaker_scores[speaker] = score

    # Find the speaker with the highest agent score
    if speaker_scores:
        sorted_speakers = sorted(speaker_scores.items(), key=lambda x: x[1], reverse=True)

        # If there's a clear winner (higher score than others)
        if len(sorted_speakers) >= 2 and sorted_speakers[0][1] > sorted_speakers[1][1]:
            agent_speaker = sorted_speakers[0][0]
            customer_speaker = sorted_speakers[1][0]
            return agent_speaker, customer_speaker
        elif len(sorted_speakers) == 1:
            # Only one speaker, assume they're the agent
            return sorted_speakers[0][0], None

    # If we can't determine, return the speakers as-is
    speakers = list(speaker_texts.keys())
    if len(speakers) >= 2:
        return speakers[0], speakers[1]
    elif len(speakers) == 1:
        return speakers[0], None

    return None, None

def _get_chatgpt_analysis(transcript_data, agent_name):
    """
    Send transcript to ChatGPT API for analysis and satisfaction rating.

    Args:
        transcript_data (dict): Processed transcript data with speaker identification
        agent_name (str): Name of the agent

    Returns:
        str: ChatGPT analysis and satisfaction rating
    """
    # Get API key from file
    api_key = get_chatgpt_api_key()
    if not api_key:
        return "ChatGPT analysis unavailable - no API key found in OpenAI_KEY.txt"

    # Format the transcript for ChatGPT
    formatted_transcript = _format_transcript_for_chatgpt(transcript_data, agent_name)

    # Prepare the prompt
    prompt = "Make a summary of this exchange and rate the overall satisfaction of the call resolution"

    # Prepare the API request
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {
                "role": "user",
                "content": f"{prompt}\n\n{formatted_transcript}"
            }
        ],
        "max_tokens": 500,
        "temperature": 0.3
    }

    # Make the API call
    response = requests.post(
        "https://api.openai.com/v1/chat/completions",
        headers=headers,
        json=data,
        timeout=30
    )

    if response.status_code == 200:
        result = response.json()
        return result['choices'][0]['message']['content'].strip()
    else:
        raise RuntimeError(f"ChatGPT API error: {response.status_code} - {response.text}")

def _format_transcript_for_chatgpt(transcript_data, agent_name):
    """
    Format the transcript data for sending to ChatGPT.

    Args:
        transcript_data (dict): Processed transcript data
        agent_name (str): Name of the agent

    Returns:
        str: Formatted transcript text
    """
    agent_speaker = transcript_data.get('agent_speaker')
    customer_speaker = transcript_data.get('customer_speaker')
    utterances = transcript_data.get('utterances', [])

    formatted_lines = []

    for utterance in utterances:
        speaker = utterance['speaker']
        text = utterance['text']

        # Determine display name for speaker
        if speaker == agent_speaker:
            display_speaker = f"{agent_name} (Agent)"
        elif speaker == customer_speaker:
            display_speaker = "Customer"
        else:
            display_speaker = f"Speaker {speaker}"

        formatted_lines.append(f"{display_speaker}: {text}")

    return "\n".join(formatted_lines)

def extract_call_data(csv_file_path):
    """
    Extract agent name and recording URL from a CSV file.
    
    Args:
        csv_file_path (str): Path to the CSV file
        
    Returns:
        list: List of tuples containing (agent_name, recording_url)
    """
    # Read CSV file
    df = pd.read_csv(csv_file_path)
    
    # Extract agent names and recording URLs, filtering out NaN values
    call_data = []
    for _, row in df.iterrows():
        agent_name = row["Agent Full Name"]
        recording_url = row["Recording URL"]
        # Skip rows where either agent_name or recording_url is NaN
        if pd.notna(agent_name) and pd.notna(recording_url):
            call_data.append((agent_name, recording_url))
    
    return call_data

def create_call_selection_ui(call_data):
    """
    Create a UI to select which calls to process.
    
    Args:
        call_data (list): List of tuples containing (agent_name, recording_url)
        
    Returns:
        list: List of selected call data tuples
    """
    selected_calls = []
    selection_complete = threading.Event()
    
    def on_process_selected():
        for i, var in enumerate(check_vars):
            if var.get():
                selected_calls.append(call_data[i])
        root.destroy()
        selection_complete.set()
    
    # Create the main window
    root = tk.Tk()
    root.title("Call Data Selection")
    root.geometry("800x400")
    
    # Create a frame for the call data
    frame = ttk.Frame(root)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Create a scrollable area
    canvas = tk.Canvas(frame)
    scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # Add headers
    ttk.Label(scrollable_frame, text="Select", width=10).grid(row=0, column=0, padx=5, pady=5)
    ttk.Label(scrollable_frame, text="Agent Name", width=20).grid(row=0, column=1, padx=5, pady=5)
    ttk.Label(scrollable_frame, text="Recording URL", width=60).grid(row=0, column=2, padx=5, pady=5)
    
    # Add call data with checkboxes
    check_vars = []
    for i, (agent_name, recording_url) in enumerate(call_data):
        var = tk.BooleanVar()
        check_vars.append(var)
        ttk.Checkbutton(scrollable_frame, variable=var).grid(row=i+1, column=0, padx=5, pady=2)
        ttk.Label(scrollable_frame, text=agent_name).grid(row=i+1, column=1, padx=5, pady=2, sticky="w")
        url_label = ttk.Label(scrollable_frame, text=recording_url[:50] + "...")
        url_label.grid(row=i+1, column=2, padx=5, pady=2, sticky="w")
    
    # Add buttons
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=10)
    
    ttk.Button(button_frame, text="Select All", command=lambda: [var.set(True) for var in check_vars]).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Deselect All", command=lambda: [var.set(False) for var in check_vars]).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Process Selected", command=on_process_selected).pack(side=tk.RIGHT, padx=5)
    
    root.mainloop()
    selection_complete.wait()
    return selected_calls

def select_csv_file():
    """
    Open a file dialog to select a CSV file.

    Returns:
        str: Path to the selected CSV file or None if canceled
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    file_path = filedialog.askopenfilename(
        title="Select CSV File",
        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
    )
    root.destroy()
    return file_path if file_path else None

def get_assemblyai_api_key():
    """
    Read AssemblyAI API key from Assembly_KEY.txt file.

    Returns:
        str: API key or raises RuntimeError if file not found or empty
    """
    try:
        with open("Assembly_KEY.txt", "r", encoding="utf-8") as file:
            api_key = file.read().strip()
            if api_key:
                return api_key
            else:
                raise RuntimeError("Assembly_KEY.txt file is empty")
    except FileNotFoundError:
        raise RuntimeError("Assembly_KEY.txt file not found")
    except Exception as e:
        raise RuntimeError(f"Error reading Assembly_KEY.txt: {str(e)}")

def get_chatgpt_api_key():
    """
    Read ChatGPT API key from OpenAI_KEY.txt file.

    Returns:
        str: API key or None if file not found or empty
    """
    try:
        with open("OpenAI_KEY.txt", "r", encoding="utf-8") as file:
            api_key = file.read().strip()
            return api_key if api_key else None
    except FileNotFoundError:
        print("Warning: OpenAI_KEY.txt file not found. ChatGPT analysis will be skipped.")
        return None
    except Exception as e:
        print(f"Warning: Error reading OpenAI_KEY.txt: {str(e)}. ChatGPT analysis will be skipped.")
        return None

def init_database():
    """
    Initialize the SQLite database and create the call_analysis table if it doesn't exist.

    Returns:
        str: Path to the database file
    """
    db_path = "call_analysis.db"

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Create table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS call_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_time TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                sentiment_score REAL NOT NULL,
                assemblyai_summary TEXT,
                chatgpt_analysis TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

        return db_path

    except Exception as e:
        print(f"Warning: Error initializing database: {str(e)}")
        return None

def save_to_database(results):
    """
    Save call analysis results to SQLite database.

    Args:
        results (list): List of tuples containing (agent_name, summary, sentiment_score, chatgpt_summary)

    Returns:
        bool: True if successful, False otherwise
    """
    db_path = init_database()
    if not db_path:
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        for result in results:
            # Handle both old format (3 items) and new format (4 items) for backward compatibility
            if len(result) == 4:
                agent_name, summary, sentiment_score, chatgpt_summary = result
            else:
                agent_name, summary, sentiment_score = result
                chatgpt_summary = None

            # Convert summary to string if it's a list
            if isinstance(summary, list):
                summary_text = '\n'.join(summary)
            else:
                summary_text = str(summary) if summary else ""

            # Insert record
            cursor.execute('''
                INSERT INTO call_analysis
                (date_time, agent_name, sentiment_score, assemblyai_summary, chatgpt_analysis)
                VALUES (?, ?, ?, ?, ?)
            ''', (current_datetime, agent_name, sentiment_score, summary_text, chatgpt_summary))

        conn.commit()
        conn.close()

        print(f"Successfully saved {len(results)} records to database: {db_path}")
        return True

    except Exception as e:
        print(f"Error saving to database: {str(e)}")
        return False

def view_database_records(limit=10):
    """
    View recent records from the database for verification.

    Args:
        limit (int): Number of recent records to display

    Returns:
        list: List of database records
    """
    db_path = "call_analysis.db"

    if not os.path.exists(db_path):
        print("Database file not found.")
        return []

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, date_time, agent_name, sentiment_score,
                   SUBSTR(assemblyai_summary, 1, 50) as summary_preview,
                   SUBSTR(chatgpt_analysis, 1, 50) as chatgpt_preview,
                   created_at
            FROM call_analysis
            ORDER BY created_at DESC
            LIMIT ?
        ''', (limit,))

        records = cursor.fetchall()
        conn.close()

        if records:
            print(f"\nRecent {len(records)} database records:")
            print("-" * 120)
            print(f"{'ID':<4} {'Date/Time':<20} {'Agent':<15} {'Sentiment':<10} {'Summary Preview':<25} {'ChatGPT Preview':<25}")
            print("-" * 120)

            for record in records:
                print(f"{record[0]:<4} {record[1]:<20} {record[2]:<15} {record[3]:<10.2f} {record[4]:<25} {record[5] or 'N/A':<25}")
        else:
            print("No records found in database.")

        return records

    except Exception as e:
        print(f"Error reading from database: {str(e)}")
        return []

def generate_pdf_report(results, output_path=None):
    """
    Generate a PDF report with call analysis results including ChatGPT analysis.

    Args:
        results (list): List of tuples containing (agent_name, summary, sentiment_score, chatgpt_summary)
        output_path (str, optional): Path to save the PDF. If None, generates a default name.

    Returns:
        str: Path to the generated PDF file
    """
    if output_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"call_analysis_report_{timestamp}.pdf"
    
    # Create the PDF document
    doc = SimpleDocTemplate(output_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Create custom styles
    title_style = styles["Heading1"]
    heading_style = styles["Heading2"]
    normal_style = styles["Normal"]
    
    # Create a list to hold the flowables
    elements = []
    
    # Add title
    elements.append(Paragraph("Call Analysis Report", title_style))
    elements.append(Spacer(1, 12))
    elements.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))
    elements.append(Spacer(1, 24))
    
    # Add each call analysis
    for i, result in enumerate(results):
        # Handle both old format (3 items) and new format (4 items) for backward compatibility
        if len(result) == 4:
            agent_name, summary, sentiment_score, chatgpt_summary = result
        else:
            agent_name, summary, sentiment_score = result
            chatgpt_summary = None
        # Add agent name
        elements.append(Paragraph(f"Call {i+1}: {agent_name}", heading_style))
        elements.append(Spacer(1, 6))
        
        # Add sentiment score with color coding and interpretation
        sentiment_text = f"Sentiment Score: {sentiment_score:.2f}"
        
        # More granular sentiment interpretation
        if sentiment_score > 0.6:
            sentiment_color = colors.green
            sentiment_text += " (Very Positive)"
        elif sentiment_score > 0.2:
            sentiment_color = colors.forestgreen
            sentiment_text += " (Positive)"
        elif sentiment_score > -0.2:
            sentiment_color = colors.black
            sentiment_text += " (Neutral)"
        elif sentiment_score > -0.6:
            sentiment_color = colors.orangered
            sentiment_text += " (Negative)"
        else:
            sentiment_color = colors.red
            sentiment_text += " (Very Negative)"
            
        sentiment_style = ParagraphStyle(
            "sentiment", 
            parent=normal_style, 
            textColor=sentiment_color,
            fontName="Helvetica-Bold"
        )
        elements.append(Paragraph(sentiment_text, sentiment_style))
        elements.append(Spacer(1, 6))
        
        # Add summary
        elements.append(Paragraph("Summary:", styles["Heading3"]))
        # Split summary by bullet points if it's in that format
        if isinstance(summary, list):
            for bullet in summary:
                elements.append(Paragraph(f"• {bullet}", normal_style))
                elements.append(Spacer(1, 3))
        else:
            # Handle string format (might be bullet-separated with newlines)
            summary_parts = summary.split("\n")
            for part in summary_parts:
                if part.strip():
                    elements.append(Paragraph(part.strip(), normal_style))
                    elements.append(Spacer(1, 3))

        # Add ChatGPT analysis section if available
        if chatgpt_summary:
            elements.append(Spacer(1, 12))
            elements.append(Paragraph("ChatGPT Analysis & Satisfaction Rating:", styles["Heading3"]))
            elements.append(Spacer(1, 6))

            # Split ChatGPT response by lines for better formatting
            chatgpt_lines = chatgpt_summary.split('\n')
            for line in chatgpt_lines:
                if line.strip():
                    elements.append(Paragraph(line.strip(), normal_style))
                    elements.append(Spacer(1, 3))

        elements.append(Spacer(1, 24))
    
    # Build the PDF
    doc.build(elements)
    return output_path

def process_calls_with_progress(selected_calls):
    """
    Process selected calls with a progress bar UI.

    Args:
        selected_calls (list): List of tuples containing (agent_name, recording_url)

    Returns:
        list: List of results (agent_name, summary, sentiment_score, chatgpt_summary)
    """
    results = []
    error_messages = []
    
    # Create a new Tk window for the progress bar
    root = tk.Tk()
    root.title("Processing Calls")
    root.geometry("400x150")
    
    # Add labels and progress bar
    tk.Label(root, text="Analyzing call recordings...", font=("Arial", 12)).pack(pady=10)
    
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(root, variable=progress_var, length=350, mode="determinate")
    progress_bar.pack(pady=10)
    
    status_var = tk.StringVar(value="Starting...")
    status_label = tk.Label(root, textvariable=status_var, font=("Arial", 10))
    status_label.pack(pady=5)
    
    total_calls = len(selected_calls)
    
    # Process each call one by one
    for i, (agent_name, recording_url) in enumerate(selected_calls):
        # Update progress bar
        progress_var.set((i / total_calls) * 100)
        status_var.set(f"Processing call {i+1} of {total_calls}: {agent_name}")
        root.update()
        
        try:
            # Process the call
            summary, sentiment_score, chatgpt_summary = analyze_audio(recording_url, agent_name)
            results.append((agent_name, summary, sentiment_score, chatgpt_summary))
        except Exception as e:
            error_messages.append(f"Failed to process call for {agent_name}: {str(e)}")
    
    # Complete the progress
    progress_var.set(100)
    status_var.set("Processing complete!")
    root.update()
    
    # Close the window after a short delay
    root.after(1000, root.destroy)
    root.mainloop()
    
    # Show any error messages
    if error_messages:
        error_text = "\n\n".join(error_messages)
        messagebox.showerror("Errors Occurred", f"Some calls could not be processed:\n\n{error_text}")
    
    return results

# Example usage
if __name__ == "__main__":
    # Let user select CSV file
    csv_path = select_csv_file()
    
    if not csv_path:
        print("No CSV file selected. Exiting.")
        exit()
    
    call_data = extract_call_data(csv_path)
    
    if not call_data:
        print("No valid call data found in the CSV file.")
        exit()
    
    selected_calls = create_call_selection_ui(call_data)

    if not selected_calls:
        print("No calls selected for processing. Exiting.")
        exit()

    # Process selected calls with progress bar
    results = process_calls_with_progress(selected_calls)
    
    # Save results to database and generate PDF report
    if results:
        # Save to database
        db_saved = save_to_database(results)

        # Generate PDF report
        pdf_path = generate_pdf_report(results)

        # Show success message
        success_message = f"PDF report generated: {pdf_path}"
        if db_saved:
            success_message += f"\nResults also saved to database: call_analysis.db"

        messagebox.showinfo("Success", success_message)
        print(success_message.replace('\n', ' | '))
