#!/usr/bin/env python3
"""
Test script for RingCX access token functions.
This script demonstrates how to use the getRingCX functions.
"""

import sys
import os

def test_ringcx_functions():
    """Test the RingCX authentication functions."""
    
    try:
        # Import the functions from getRingCX
        from getRingCX import (
            get_ringcx_access_token, 
            get_ringcx_token_from_file, 
            extract_access_token,
            RC_ACCESS_TOKEN,
            LOGIN_URL
        )
        
        print("Testing RingCX Authentication Functions")
        print("=" * 50)
        
        # Test 1: Using hardcoded token
        print("\n1. Testing with hardcoded RC_ACCESS_TOKEN...")
        try:
            auth_response = get_ringcx_access_token()
            print("✓ Authentication request successful")
            print(f"Response type: {type(auth_response)}")
            
            if isinstance(auth_response, dict):
                print(f"Response keys: {list(auth_response.keys())}")
                
                # Try to extract access token
                try:
                    access_token = extract_access_token(auth_response)
                    print(f"✓ Access token extracted successfully")
                    print(f"Token preview: {access_token[:50]}...")
                    print(f"Token length: {len(access_token)} characters")
                except Exception as e:
                    print(f"✗ Failed to extract access token: {e}")
            else:
                print(f"Response content: {str(auth_response)[:100]}...")
                
        except Exception as e:
            print(f"✗ Authentication failed: {e}")
        
        # Test 2: Using token from file (if file exists)
        print("\n2. Testing with RC_ACCESS_TOKEN from file...")
        token_file = "RC_ACCESS_TOKEN.txt"
        
        if os.path.exists(token_file):
            try:
                auth_response = get_ringcx_token_from_file(token_file)
                access_token = extract_access_token(auth_response)
                print(f"✓ Successfully authenticated using {token_file}")
                print(f"Token preview: {access_token[:50]}...")
            except Exception as e:
                print(f"✗ Failed to authenticate using {token_file}: {e}")
        else:
            print(f"ℹ {token_file} not found, skipping file-based test")
            print(f"  You can create this file with your RC_ACCESS_TOKEN to test file-based authentication")
        
        # Test 3: Show current configuration
        print("\n3. Current Configuration:")
        print(f"RC_ACCESS_TOKEN: {RC_ACCESS_TOKEN[:50]}...")
        print(f"LOGIN_URL: {LOGIN_URL}")
        
        print("\n" + "=" * 50)
        print("Test completed!")
        
    except ImportError as e:
        print(f"✗ Error importing getRingCX functions: {e}")
        print("Make sure getRingCX.py is in the same directory")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

def create_sample_token_file():
    """Create a sample RC_ACCESS_TOKEN.txt file for testing."""
    
    from getRingCX import RC_ACCESS_TOKEN
    
    try:
        with open("RC_ACCESS_TOKEN.txt", "w", encoding="utf-8") as file:
            file.write(RC_ACCESS_TOKEN)
        print("✓ Created RC_ACCESS_TOKEN.txt with sample token")
    except Exception as e:
        print(f"✗ Failed to create token file: {e}")

if __name__ == "__main__":
    print("RingCX Authentication Test")
    print("=" * 30)
    
    # Check if user wants to create sample file
    if len(sys.argv) > 1 and sys.argv[1] == "--create-file":
        create_sample_token_file()
        print()
    
    # Run the tests
    test_ringcx_functions()
    
    print("\nUsage:")
    print("  python test_ringcx.py                 # Run tests")
    print("  python test_ringcx.py --create-file   # Create sample token file and run tests")
