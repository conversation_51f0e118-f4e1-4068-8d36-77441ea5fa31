#!/usr/bin/env python3
"""
Installation script for RingCX JWT authentication dependencies.
This script installs the required packages for JWT authentication.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}: {e}")
        return False

def check_package(package_name):
    """Check if a package is already installed."""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """Main installation function."""
    
    print("RingCX JWT Authentication - Dependency Installation")
    print("=" * 60)
    
    # List of required packages
    packages = [
        ("PyJWT", "jwt"),
        ("cryptography", "cryptography"),
        ("requests", "requests")
    ]
    
    print("\nChecking and installing required packages...")
    
    all_installed = True
    
    for pip_name, import_name in packages:
        print(f"\nChecking {pip_name}...")
        
        if check_package(import_name):
            print(f"✓ {pip_name} is already installed")
        else:
            print(f"⚠ {pip_name} not found, installing...")
            if install_package(pip_name):
                print(f"✓ {pip_name} installed successfully")
            else:
                print(f"✗ Failed to install {pip_name}")
                all_installed = False
    
    print("\n" + "=" * 60)
    
    if all_installed:
        print("✓ All dependencies installed successfully!")
        print("\nNext steps:")
        print("1. Update JWT_CREDENTIALS.json with your RingCentral credentials")
        print("2. Run: python test_jwt_auth.py")
        print("3. Or run: python getRingCX.py")
    else:
        print("✗ Some dependencies failed to install")
        print("Try installing manually:")
        print("  pip install PyJWT cryptography requests")
    
    print("\nFor more information, see JWT_Authentication_README.md")

if __name__ == "__main__":
    main()
