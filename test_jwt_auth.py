#!/usr/bin/env python3
"""
Test script for RingCX JWT authentication.
This script demonstrates how to use JWT authentication with RingCX.
"""

import sys
import os
import json

def test_jwt_authentication():
    """Test JWT authentication functions."""
    
    try:
        # Import the functions from getRingCX
        from getRingCX import (
            get_ringcx_access_token_with_jwt,
            get_ringcentral_access_token_with_jwt,
            create_jwt_token,
            read_jwt_credentials_from_file,
            extract_access_token
        )
        
        print("Testing RingCX JWT Authentication")
        print("=" * 50)
        
        # Test 1: Check if JWT credentials file exists
        print("\n1. Checking JWT credentials file...")
        credentials_file = "JWT_CREDENTIALS.json"
        
        if os.path.exists(credentials_file):
            try:
                credentials = read_jwt_credentials_from_file(credentials_file)
                print("✓ JWT credentials file found and parsed")
                print(f"Client ID: {credentials['client_id']}")
                print(f"Server URL: {credentials.get('server_url', 'default')}")
                
                # Check if credentials are placeholder values
                if credentials['client_id'] == "your_ringcentral_client_id":
                    print("⚠ Warning: Using placeholder credentials - update JWT_CREDENTIALS.json with real values")
                    return
                    
            except Exception as e:
                print(f"✗ Error reading JWT credentials: {e}")
                return
        else:
            print(f"✗ {credentials_file} not found")
            print("Create this file with your RingCentral JWT credentials")
            return
        
        # Test 2: Test JWT token creation
        print("\n2. Testing JWT token creation...")
        try:
            jwt_token = create_jwt_token(
                credentials["client_id"],
                credentials["client_secret"],
                credentials["jwt_private_key"],
                credentials.get("server_url")
            )
            print("✓ JWT token created successfully")
            print(f"JWT token preview: {jwt_token[:50]}...")
        except Exception as e:
            print(f"✗ JWT token creation failed: {e}")
            return
        
        # Test 3: Test RingCentral access token
        print("\n3. Testing RingCentral access token...")
        try:
            rc_response = get_ringcentral_access_token_with_jwt(
                credentials["client_id"],
                credentials["client_secret"],
                credentials["jwt_private_key"],
                credentials.get("server_url")
            )
            print("✓ RingCentral access token obtained")
            print(f"Response keys: {list(rc_response.keys())}")
            
            rc_access_token = rc_response.get("access_token")
            if rc_access_token:
                print(f"RC access token preview: {rc_access_token[:50]}...")
            
        except Exception as e:
            print(f"✗ RingCentral authentication failed: {e}")
            return
        
        # Test 4: Test RingCX access token
        print("\n4. Testing RingCX access token...")
        try:
            ringcx_response = get_ringcx_access_token_with_jwt()
            print("✓ RingCX access token obtained")
            print(f"Response keys: {list(ringcx_response.keys())}")
            
            ringcx_access_token = extract_access_token(ringcx_response)
            print(f"RingCX access token preview: {ringcx_access_token[:50]}...")
            
        except Exception as e:
            print(f"✗ RingCX authentication failed: {e}")
            return
        
        print("\n" + "=" * 50)
        print("✓ All JWT authentication tests passed!")
        
    except ImportError as e:
        print(f"✗ Error importing functions: {e}")
        print("Make sure getRingCX.py is in the same directory")
        print("Install required packages: pip install -r requirements.txt")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

def create_sample_jwt_credentials():
    """Create a sample JWT credentials file."""
    
    sample_credentials = {
        "client_id": "your_ringcentral_client_id",
        "client_secret": "your_ringcentral_client_secret", 
        "jwt_private_key": "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----",
        "server_url": "https://platform.ringcentral.com"
    }
    
    try:
        with open("JWT_CREDENTIALS.json", "w", encoding="utf-8") as file:
            json.dump(sample_credentials, file, indent=4)
        print("✓ Created JWT_CREDENTIALS.json with sample values")
        print("⚠ Update this file with your actual RingCentral credentials")
    except Exception as e:
        print(f"✗ Failed to create credentials file: {e}")

def show_jwt_setup_instructions():
    """Show instructions for setting up JWT authentication."""
    
    print("\nJWT Authentication Setup Instructions:")
    print("=" * 50)
    print("1. Create a RingCentral application at https://developers.ringcentral.com")
    print("2. Generate a public/private key pair for JWT")
    print("3. Upload the public key to your RingCentral application")
    print("4. Update JWT_CREDENTIALS.json with:")
    print("   - client_id: Your application's client ID")
    print("   - client_secret: Your application's client secret")
    print("   - jwt_private_key: Your private key (PEM format)")
    print("   - server_url: RingCentral server URL (usually https://platform.ringcentral.com)")
    print("\n5. Install required packages:")
    print("   pip install -r requirements.txt")
    print("\n6. Run this test script again to verify authentication")

if __name__ == "__main__":
    print("RingCX JWT Authentication Test")
    print("=" * 40)
    
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--create-sample":
            create_sample_jwt_credentials()
            print()
        elif sys.argv[1] == "--help":
            show_jwt_setup_instructions()
            sys.exit(0)
    
    # Run the tests
    test_jwt_authentication()
    
    print("\nUsage:")
    print("  python test_jwt_auth.py                 # Run JWT tests")
    print("  python test_jwt_auth.py --create-sample # Create sample credentials file")
    print("  python test_jwt_auth.py --help          # Show setup instructions")
