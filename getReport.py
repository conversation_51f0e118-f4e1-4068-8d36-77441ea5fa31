#!/usr/bin/env python3
"""
RingCX API Report Query Module
This module queries the RingCX API to retrieve call details using authentication from rc_auth.py.
"""

import requests
import json
from datetime import datetime, timedelta
from rc_auth import get_ringcx_access_token_from_rc_credentials, extract_access_token

# RingCX API endpoints
RINGCX_BASE_URL = "https://ringcx.ringcentral.com/voice"
REPORTS_STREAMING_ENDPOINT = "/api/v1/admin/accounts/********/reportsStreaming"

def get_ringcx_access_token():
    """
    Get RingCX access token using rc_auth.py authentication.
    
    Returns:
        str: RingCX access token
        
    Raises:
        RuntimeError: If authentication fails
    """
    try:
        response = get_ringcx_access_token_from_rc_credentials()
        return extract_access_token(response)
    except Exception as e:
        raise RuntimeError(f"Failed to get RingCX access token: {str(e)}")

def create_call_report_request(start_date=None, end_date=None, include_no_answers=False):
    """
    Create a call report request payload.
    
    Args:
        start_date (str, optional): Start date in ISO format. Defaults to yesterday.
        end_date (str, optional): End date in ISO format. Defaults to today.
        include_no_answers (bool): Whether to include no-answer calls. Default: False.
        
    Returns:
        dict: Request payload for RingCX API
    """
    # Set default dates if not provided
    if not start_date:
        yesterday = datetime.now() - timedelta(days=1)
        start_date = yesterday.strftime("%Y-%m-%dT00:00:00.000-0000")
    
    if not end_date:
        today = datetime.now()
        end_date = today.strftime("%Y-%m-%dT23:59:59.999-0000")
    
    # Create the request payload
    payload = {
        "reportType": "GLOBAL_CALL_TYPE_DELIMITED",
        "reportCriteria": {
            "criteriaType": "GLOBAL_CALL_TYPE_CRITERIA",
            "startDate": start_date,
            "endDate": end_date,
            "containGates": True,
            "containCampaigns": True,
            "containIvrStudios": True,
            "containCloudProfiles": True,
            "containTracNumbers": True,
            "containAgents": True,
            "includeNoAnswers": include_no_answers
        }
    }
    
    return payload

def query_call_report_with_endpoint(endpoint, start_date=None, end_date=None, include_no_answers=False, access_token=None):
    """
    Query RingCX API for call details report using a specific endpoint.

    Args:
        endpoint (str): API endpoint to use
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
        include_no_answers (bool): Whether to include no-answer calls
        access_token (str, optional): RingCX access token. If None, will get automatically.

    Returns:
        dict: API response containing call report data

    Raises:
        RuntimeError: If API request fails
    """
    try:
        # Get access token if not provided
        if not access_token:
            access_token = get_ringcx_access_token()

        # Create request payload
        payload = create_call_report_request(start_date, end_date, include_no_answers)

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json"
        }

        # Make API request
        url = f"{RINGCX_BASE_URL}{endpoint}"

        print(f"Making request to: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")

        response = requests.post(url, headers=headers, json=payload, timeout=30)

        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content type: {response.headers.get('content-type', 'unknown')}")
        print(f"Response text preview: {response.text[:500]}...")

        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text}")
                # Return the raw text for analysis
                return {"raw_response": response.text, "status": "json_parse_error"}
        else:
            raise RuntimeError(f"API request failed: {response.status_code} - {response.text}")

    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"Error querying call report: {str(e)}")

def query_call_report(start_date=None, end_date=None, include_no_answers=False, access_token=None):
    """
    Query RingCX API for call details report.

    Args:
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
        include_no_answers (bool): Whether to include no-answer calls
        access_token (str, optional): RingCX access token. If None, will get automatically.

    Returns:
        dict: API response containing call report data

    Raises:
        RuntimeError: If API request fails
    """
    return query_call_report_with_endpoint(
        REPORTS_STREAMING_ENDPOINT,
        start_date,
        end_date,
        include_no_answers,
        access_token
    )

def parse_csv_data(csv_data):
    """
    Parse CSV data and return basic statistics.

    Args:
        csv_data (str): CSV data from RingCX API

    Returns:
        dict: Basic statistics about the CSV data
    """
    if not csv_data:
        return {"error": "No data provided"}

    lines = csv_data.strip().split('\n')

    if len(lines) < 1:
        return {"error": "Empty data"}

    # Get header line
    header_line = lines[0] if lines else ""
    headers = [h.strip('"') for h in header_line.split(',')]

    # Count data rows (excluding header)
    data_rows = len(lines) - 1 if len(lines) > 1 else 0

    stats = {
        "total_lines": len(lines),
        "header_count": len(headers),
        "data_rows": data_rows,
        "headers": headers[:10],  # First 10 headers
        "sample_data": lines[1] if len(lines) > 1 else None,
        "data_size": len(csv_data)
    }

    return stats

def save_csv_with_timestamp(csv_data, filename_prefix="ringcx_call_report"):
    """
    Save CSV data to a file with timestamp.

    Args:
        csv_data (str): CSV data to save
        filename_prefix (str): Prefix for the filename

    Returns:
        str: Full path of the saved file

    Raises:
        RuntimeError: If file saving fails
    """
    try:
        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.csv"

        # Save the file
        with open(filename, 'w', encoding='utf-8') as file:
            file.write(csv_data)

        print(f"✓ CSV file saved: {filename}")
        return filename

    except Exception as e:
        raise RuntimeError(f"Failed to save CSV file: {str(e)}")





def get_yesterday_call_report(save_file=False, filename_prefix="ringcx_call_report"):
    """
    Get call report for yesterday (last 24 hours from now).

    Args:
        save_file (bool): Whether to automatically save the CSV file
        filename_prefix (str): Prefix for the saved filename

    Returns:
        str: CSV data containing call reports, or None if failed

    Raises:
        RuntimeError: If query fails
    """
    try:
        # Calculate yesterday's date range
        now = datetime.now()
        yesterday_start = now - timedelta(days=1)
        yesterday_end = now

        # Format dates for RingCX API
        start_date = yesterday_start.strftime("%Y-%m-%dT00:00:00.000-0000")
        end_date = yesterday_end.strftime("%Y-%m-%dT23:59:59.999-0000")

        print(f"Querying call reports from {start_date} to {end_date}")

        # Query the call report
        response = query_call_report(start_date=start_date, end_date=end_date)

        # Check if we got CSV data
        if isinstance(response, dict) and "raw_response" in response:
            csv_data = response["raw_response"]
            print("✓ Call report retrieved successfully")

            # Parse CSV data for statistics
            stats = parse_csv_data(csv_data)
            print(f"Data rows: {stats.get('data_rows', 0)}")
            print(f"Data size: {stats.get('data_size', 0)} characters")

            return csv_data
        else:
            print("✗ Unexpected response format")
            return None

    except Exception as e:
        print(f"✗ Failed to get yesterday's call report: {e}")
        raise

# Main execution
if __name__ == "__main__":
    print("RingCX Call Report Query Tool")
    print("Getting yesterday's call data...")
    print("=" * 60)

    try:
        # Get yesterday's call report
        csv_data = get_yesterday_call_report()

        if csv_data:
            print("\n" + "=" * 60)
            print("SUCCESS: Call report retrieved!")
            print("=" * 60)
            print(f"Data preview (first 500 characters):")
            print(csv_data[:500] + "...")
            print(f"\nTotal data size: {len(csv_data)} characters")

            # Save CSV file with timestamp
            try:
                saved_filename = save_csv_with_timestamp(csv_data)
                print(f"File size: {len(csv_data)} bytes")
                print("CSV data is ready for processing.")
            except Exception as e:
                print(f"⚠ Warning: Failed to save CSV file: {e}")
                print("CSV data is still available in memory for processing.")
        else:
            print("\n" + "=" * 60)
            print("FAILED: No call data retrieved")
            print("=" * 60)

    except Exception as e:
        print(f"\n✗ Error: {e}")
        print("Check your RingCX access token and API permissions.")
