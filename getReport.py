#!/usr/bin/env python3
"""
RingCX API Report Query Module
This module queries the RingCX API to retrieve call details using authentication from rc_auth.py.
"""

import requests
import json
from datetime import datetime, timedelta
from rc_auth import get_ringcx_access_token_from_rc_credentials, extract_access_token

# RingCX API endpoints
RINGCX_BASE_URL = "https://ringcx.ringcentral.com/voice"
REPORTS_STREAMING_ENDPOINT = "/api/v1/admin/accounts/********/reportsStreaming"

def get_ringcx_access_token():
    """
    Get RingCX access token using rc_auth.py authentication.
    
    Returns:
        str: RingCX access token
        
    Raises:
        RuntimeError: If authentication fails
    """
    try:
        response = get_ringcx_access_token_from_rc_credentials()
        return extract_access_token(response)
    except Exception as e:
        raise RuntimeError(f"Failed to get RingCX access token: {str(e)}")

def create_call_report_request(start_date=None, end_date=None, include_no_answers=False):
    """
    Create a call report request payload.
    
    Args:
        start_date (str, optional): Start date in ISO format. Defaults to yesterday.
        end_date (str, optional): End date in ISO format. Defaults to today.
        include_no_answers (bool): Whether to include no-answer calls. Default: False.
        
    Returns:
        dict: Request payload for RingCX API
    """
    # Set default dates if not provided
    if not start_date:
        yesterday = datetime.now() - timedelta(days=1)
        start_date = yesterday.strftime("%Y-%m-%dT00:00:00.000-0000")
    
    if not end_date:
        today = datetime.now()
        end_date = today.strftime("%Y-%m-%dT23:59:59.999-0000")
    
    # Create the request payload
    payload = {
        "reportType": "GLOBAL_CALL_TYPE_DELIMITED",
        "reportCriteria": {
            "criteriaType": "GLOBAL_CALL_TYPE_CRITERIA",
            "startDate": start_date,
            "endDate": end_date,
            "containGates": True,
            "containCampaigns": True,
            "containIvrStudios": True,
            "containCloudProfiles": True,
            "containTracNumbers": True,
            "containAgents": True,
            "includeNoAnswers": include_no_answers
        }
    }
    
    return payload

def query_call_report_with_endpoint(endpoint, start_date=None, end_date=None, include_no_answers=False, access_token=None):
    """
    Query RingCX API for call details report using a specific endpoint.

    Args:
        endpoint (str): API endpoint to use
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
        include_no_answers (bool): Whether to include no-answer calls
        access_token (str, optional): RingCX access token. If None, will get automatically.

    Returns:
        dict: API response containing call report data

    Raises:
        RuntimeError: If API request fails
    """
    try:
        # Get access token if not provided
        if not access_token:
            access_token = get_ringcx_access_token()

        # Create request payload
        payload = create_call_report_request(start_date, end_date, include_no_answers)

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json"
        }

        # Make API request
        url = f"{RINGCX_BASE_URL}{endpoint}"

        print(f"Making request to: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")

        response = requests.post(url, headers=headers, json=payload, timeout=30)

        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content type: {response.headers.get('content-type', 'unknown')}")
        print(f"Response text preview: {response.text[:500]}...")

        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text}")
                # Return the raw text for analysis
                return {"raw_response": response.text, "status": "json_parse_error"}
        else:
            raise RuntimeError(f"API request failed: {response.status_code} - {response.text}")

    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"Error querying call report: {str(e)}")

def query_call_report(start_date=None, end_date=None, include_no_answers=False, access_token=None):
    """
    Query RingCX API for call details report.

    Args:
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
        include_no_answers (bool): Whether to include no-answer calls
        access_token (str, optional): RingCX access token. If None, will get automatically.

    Returns:
        dict: API response containing call report data

    Raises:
        RuntimeError: If API request fails
    """
    return query_call_report_with_endpoint(
        REPORTS_STREAMING_ENDPOINT,
        start_date,
        end_date,
        include_no_answers,
        access_token
    )

def parse_csv_data(csv_data):
    """
    Parse CSV data and return basic statistics.

    Args:
        csv_data (str): CSV data from RingCX API

    Returns:
        dict: Basic statistics about the CSV data
    """
    if not csv_data:
        return {"error": "No data provided"}

    lines = csv_data.strip().split('\n')

    if len(lines) < 1:
        return {"error": "Empty data"}

    # Get header line
    header_line = lines[0] if lines else ""
    headers = [h.strip('"') for h in header_line.split(',')]

    # Count data rows (excluding header)
    data_rows = len(lines) - 1 if len(lines) > 1 else 0

    stats = {
        "total_lines": len(lines),
        "header_count": len(headers),
        "data_rows": data_rows,
        "headers": headers[:10],  # First 10 headers
        "sample_data": lines[1] if len(lines) > 1 else None,
        "data_size": len(csv_data)
    }

    return stats

def filter_csv_data(csv_data, required_fields=None):
    """
    Filter CSV data to include only specified fields and drop rows with empty connected_name.

    Args:
        csv_data (str): Raw CSV data from RingCX API
        required_fields (list): List of field names to include. If None, uses default fields.

    Returns:
        str: Filtered CSV data
    """
    if not csv_data:
        return ""

    # Default fields to include
    if required_fields is None:
        required_fields = [
            "source_name",
            "ANI",
            "connected_dts",
            "connected_duration",
            "connected_name",
            "recording_url"
        ]

    lines = csv_data.strip().split('\n')
    if len(lines) < 2:  # Need at least header + 1 data row
        return ""

    # Parse header line
    header_line = lines[0]
    headers = [h.strip('"') for h in header_line.split(',')]

    # Find indices of required fields
    field_indices = {}
    for field in required_fields:
        try:
            field_indices[field] = headers.index(field)
        except ValueError:
            print(f"Warning: Field '{field}' not found in CSV headers")
            return csv_data  # Return original if field missing

    # Find connected_name index for filtering
    connected_name_index = field_indices.get("connected_name")

    # Create filtered header
    filtered_header = ','.join([f'"{field}"' for field in required_fields])
    filtered_lines = [filtered_header]

    # Process data rows
    filtered_count = 0
    dropped_count = 0

    for line in lines[1:]:
        if not line.strip():  # Skip empty lines
            continue

        # Split the line (handle CSV with quoted fields)
        fields = []
        current_field = ""
        in_quotes = False

        for char in line:
            if char == '"':
                in_quotes = not in_quotes
                current_field += char
            elif char == ',' and not in_quotes:
                fields.append(current_field)
                current_field = ""
            else:
                current_field += char

        # Add the last field
        if current_field:
            fields.append(current_field)

        # Check if we have enough fields
        if len(fields) < len(headers):
            continue

        # Check if connected_name is empty (skip if empty)
        if connected_name_index is not None:
            connected_name_value = fields[connected_name_index].strip('"').strip()
            if not connected_name_value:
                dropped_count += 1
                continue

        # Extract required fields
        filtered_fields = []
        for field in required_fields:
            field_index = field_indices[field]
            if field_index < len(fields):
                filtered_fields.append(fields[field_index])
            else:
                filtered_fields.append('""')  # Empty field if missing

        # Add filtered row
        filtered_line = ','.join(filtered_fields)
        filtered_lines.append(filtered_line)
        filtered_count += 1

    print(f"✓ CSV filtered: {filtered_count} rows kept, {dropped_count} rows dropped (empty connected_name)")

    return '\n'.join(filtered_lines)

def save_csv_with_timestamp(csv_data, filename_prefix="ringcx_call_report"):
    """
    Save CSV data to a file with timestamp.

    Args:
        csv_data (str): CSV data to save
        filename_prefix (str): Prefix for the filename

    Returns:
        str: Full path of the saved file

    Raises:
        RuntimeError: If file saving fails
    """
    try:
        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.csv"

        # Save the file
        with open(filename, 'w', encoding='utf-8') as file:
            file.write(csv_data)

        print(f"✓ CSV file saved: {filename}")
        return filename

    except Exception as e:
        raise RuntimeError(f"Failed to save CSV file: {str(e)}")





def get_yesterday_call_report(save_file=False, filename_prefix="ringcx_call_report", days=1):
    """
    Get call report for the last N days.

    Args:
        save_file (bool): Whether to automatically save the CSV file
        filename_prefix (str): Prefix for the saved filename
        days (int): Number of days to query (default: 1 for yesterday)

    Returns:
        str: CSV data containing call reports, or None if failed

    Raises:
        RuntimeError: If query fails
    """
    try:
        # Calculate date range for the last N days
        now = datetime.now()
        start_time = now - timedelta(days=days)
        end_time = now

        # Format dates for RingCX API
        start_date = start_time.strftime("%Y-%m-%dT00:00:00.000-0000")
        end_date = end_time.strftime("%Y-%m-%dT23:59:59.999-0000")

        print(f"Querying call reports from {start_date} to {end_date}")

        # Query the call report
        response = query_call_report(start_date=start_date, end_date=end_date)

        # Check if we got CSV data
        if isinstance(response, dict) and "raw_response" in response:
            csv_data = response["raw_response"]
            print("✓ Call report retrieved successfully")

            # Parse CSV data for statistics (before filtering)
            stats = parse_csv_data(csv_data)
            print(f"Original data rows: {stats.get('data_rows', 0)}")
            print(f"Original data size: {stats.get('data_size', 0)} characters")

            # Filter CSV data to include only required fields
            filtered_csv_data = filter_csv_data(csv_data)

            # Parse filtered data for statistics
            filtered_stats = parse_csv_data(filtered_csv_data)
            print(f"Filtered data rows: {filtered_stats.get('data_rows', 0)}")
            print(f"Filtered data size: {filtered_stats.get('data_size', 0)} characters")

            # Save file if requested (save filtered data)
            if save_file:
                try:
                    saved_filename = save_csv_with_timestamp(filtered_csv_data, filename_prefix)
                    print(f"✓ Filtered file automatically saved: {saved_filename}")
                except Exception as e:
                    print(f"⚠ Warning: Auto-save failed: {e}")

            return filtered_csv_data
        else:
            print("✗ Unexpected response format")
            return None

    except Exception as e:
        print(f"✗ Failed to get yesterday's call report: {e}")
        raise

# Main execution
if __name__ == "__main__":
    print("RingCX Call Report Query Tool")
    print("Getting yesterday's call data...")
    print("=" * 60)

    try:
        # Get yesterday's call report
        csv_data = get_yesterday_call_report()

        if csv_data:
            print("\n" + "=" * 60)
            print("SUCCESS: Filtered call report retrieved!")
            print("=" * 60)
            print(f"Filtered data preview (first 500 characters):")
            print(csv_data[:500] + "...")
            print(f"\nFiltered data size: {len(csv_data)} characters")

            # Save CSV file with timestamp
            try:
                saved_filename = save_csv_with_timestamp(csv_data)
                print(f"File size: {len(csv_data)} bytes")
                print("Filtered CSV data is ready for processing.")
            except Exception as e:
                print(f"⚠ Warning: Failed to save CSV file: {e}")
                print("Filtered CSV data is still available in memory for processing.")
        else:
            print("\n" + "=" * 60)
            print("FAILED: No call data retrieved")
            print("=" * 60)

    except Exception as e:
        print(f"\n✗ Error: {e}")
        print("Check your RingCX access token and API permissions.")
