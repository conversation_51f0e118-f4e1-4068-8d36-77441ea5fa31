#!/usr/bin/env python3
"""
RingCX API Report Query Module
This module queries the RingCX API to retrieve call details using authentication from rc_auth.py.
"""

import requests
import json
from datetime import datetime, timedelta
from rc_auth import get_ringcx_access_token_from_rc_credentials, extract_access_token

# RingCX API endpoints
RINGCX_BASE_URL = "https://ringcx.ringcentral.com"
REPORTS_STREAMING_ENDPOINT = "/api/v1/admin/accounts/********/reportsStreaming"

def get_ringcx_access_token():
    """
    Get RingCX access token using rc_auth.py authentication.
    
    Returns:
        str: RingCX access token
        
    Raises:
        RuntimeError: If authentication fails
    """
    try:
        response = get_ringcx_access_token_from_rc_credentials()
        return extract_access_token(response)
    except Exception as e:
        raise RuntimeError(f"Failed to get RingCX access token: {str(e)}")

def create_call_report_request(start_date=None, end_date=None, include_no_answers=False):
    """
    Create a call report request payload.
    
    Args:
        start_date (str, optional): Start date in ISO format. Defaults to yesterday.
        end_date (str, optional): End date in ISO format. Defaults to today.
        include_no_answers (bool): Whether to include no-answer calls. Default: False.
        
    Returns:
        dict: Request payload for RingCX API
    """
    # Set default dates if not provided
    if not start_date:
        yesterday = datetime.now() - timedelta(days=1)
        start_date = yesterday.strftime("%Y-%m-%dT00:00:00.000-0000")
    
    if not end_date:
        today = datetime.now()
        end_date = today.strftime("%Y-%m-%dT23:59:59.999-0000")
    
    # Create the request payload
    payload = {
        "reportType": "GLOBAL_CALL_TYPE_DELIMITED",
        "reportCriteria": {
            "criteriaType": "GLOBAL_CALL_TYPE_CRITERIA",
            "startDate": start_date,
            "endDate": end_date,
            "containGates": True,
            "containCampaigns": True,
            "containIvrStudios": True,
            "containCloudProfiles": True,
            "containTracNumbers": True,
            "containAgents": True,
            "includeNoAnswers": include_no_answers
        }
    }
    
    return payload

def query_call_report_with_endpoint(endpoint, start_date=None, end_date=None, include_no_answers=False, access_token=None):
    """
    Query RingCX API for call details report using a specific endpoint.

    Args:
        endpoint (str): API endpoint to use
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
        include_no_answers (bool): Whether to include no-answer calls
        access_token (str, optional): RingCX access token. If None, will get automatically.

    Returns:
        dict: API response containing call report data

    Raises:
        RuntimeError: If API request fails
    """
    try:
        # Get access token if not provided
        if not access_token:
            access_token = get_ringcx_access_token()

        # Create request payload
        payload = create_call_report_request(start_date, end_date, include_no_answers)

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json"
        }

        # Make API request
        url = f"{RINGCX_BASE_URL}{endpoint}"

        print(f"Making request to: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")

        response = requests.post(url, headers=headers, json=payload, timeout=30)

        print(f"Response status: {response.status_code}")

        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"API request failed: {response.status_code} - {response.text}")

    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"Error querying call report: {str(e)}")

def query_call_report(start_date=None, end_date=None, include_no_answers=False, access_token=None):
    """
    Query RingCX API for call details report.

    Args:
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
        include_no_answers (bool): Whether to include no-answer calls
        access_token (str, optional): RingCX access token. If None, will get automatically.

    Returns:
        dict: API response containing call report data

    Raises:
        RuntimeError: If API request fails
    """
    return query_call_report_with_endpoint(
        REPORTS_STREAMING_ENDPOINT,
        start_date,
        end_date,
        include_no_answers,
        access_token
    )

def format_call_report_response(response_data):
    """
    Format the call report response for display.
    
    Args:
        response_data (dict): Response from RingCX API
        
    Returns:
        str: Formatted report summary
    """
    if not response_data:
        return "No data received"
    
    summary = []
    summary.append("RingCX Call Report Response")
    summary.append("=" * 40)
    
    # Display response keys
    summary.append(f"Response keys: {list(response_data.keys())}")
    
    # Check for common response fields
    if "reportId" in response_data:
        summary.append(f"Report ID: {response_data['reportId']}")
    
    if "status" in response_data:
        summary.append(f"Status: {response_data['status']}")
    
    if "message" in response_data:
        summary.append(f"Message: {response_data['message']}")
    
    if "data" in response_data:
        data = response_data["data"]
        if isinstance(data, list):
            summary.append(f"Number of records: {len(data)}")
            if data:
                summary.append("Sample record keys:")
                summary.append(f"  {list(data[0].keys()) if isinstance(data[0], dict) else 'Non-dict data'}")
        else:
            summary.append(f"Data type: {type(data)}")
    
    # Display full response (truncated if too long)
    response_str = json.dumps(response_data, indent=2)
    if len(response_str) > 1000:
        summary.append("\nFull response (truncated):")
        summary.append(response_str[:1000] + "...")
    else:
        summary.append("\nFull response:")
        summary.append(response_str)
    
    return "\n".join(summary)



def test_account_info(access_token):
    """
    Test getting account information to verify token works.

    Args:
        access_token (str): RingCX access token
    """
    print("\nTesting account information endpoint...")
    print("=" * 50)

    endpoints = [
        "/api/v1/admin/accounts/~",
        "/api/v1/accounts/~",
        "/api/v1/account",
        "/api/v1/admin/account"
    ]

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json"
    }

    for endpoint in endpoints:
        try:
            url = f"{RINGCX_BASE_URL}{endpoint}"
            print(f"\nTrying: {endpoint}")

            response = requests.get(url, headers=headers, timeout=10)
            print(f"  Status: {response.status_code}")

            if response.status_code == 200:
                print(f"  ✓ Success! Response: {response.text[:300]}...")
                return response.json()
            else:
                print(f"  Response: {response.text[:200]}...")

        except Exception as e:
            print(f"  Error: {str(e)}")

    return None

def test_call_report_query():
    """
    Test function to query call reports and display results.
    """
    try:
        print("Testing RingCX Call Report Query")
        print("=" * 50)

        # Test 1: Get access token
        print("\n1. Getting RingCX access token...")
        access_token = get_ringcx_access_token()
        print(f"✓ Access token obtained: {access_token[:50]}...")

        # Test 2: Test account info first
        print("\n2. Testing account information...")
        test_account_info(access_token)

        # Test 3: Try call report query
        print("\n3. Trying call report query...")
        try:
            response = query_call_report(access_token=access_token)
            print("✓ Call report query successful")

            # Format and display response
            formatted_response = format_call_report_response(response)
            print(formatted_response)

            return response
        except Exception as e:
            print(f"✗ Call report query failed: {e}")
            return None

    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return None

def query_custom_date_range():
    """
    Example function to query a custom date range.
    """
    try:
        print("\n" + "=" * 50)
        print("Custom Date Range Query Example")
        print("=" * 50)
        
        # Query for June 1, 2025 (as in your example)
        start_date = "2025-06-01T00:00:00.000-0000"
        end_date = "2025-06-01T23:59:59.999-0000"
        
        print(f"Querying calls from {start_date} to {end_date}")
        
        response = query_call_report(
            start_date=start_date,
            end_date=end_date,
            include_no_answers=False
        )
        
        print("✓ Custom date range query successful")
        formatted_response = format_call_report_response(response)
        print(formatted_response)
        
        return response
        
    except Exception as e:
        print(f"✗ Custom date range query failed: {e}")
        return None

# Main execution
if __name__ == "__main__":
    print("RingCX Call Report Query Tool")
    print("Using authentication from rc_auth.py")
    print("=" * 60)
    
    # Test basic functionality
    response = test_call_report_query()
    
    # Test custom date range
    custom_response = query_custom_date_range()
    
    print("\n" + "=" * 60)
    print("Summary:")
    print(f"✓ Basic query: {'Success' if response else 'Failed'}")
    print(f"✓ Custom date query: {'Success' if custom_response else 'Failed'}")
    
    if response or custom_response:
        print("\nThe RingCX API is accessible and responding to call report queries.")
    else:
        print("\nCheck your RingCX access token and API permissions.")
