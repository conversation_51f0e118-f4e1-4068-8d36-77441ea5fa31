#!/usr/bin/env python3
"""
RingCentral and RingCX authentication using rc-credentials.json file.
This module provides functions to authenticate with RingCentral and RingCX APIs
using the credentials from rc-credentials.json file.
"""

import requests
import json
import base64

# API endpoints
RINGCENTRAL_TOKEN_URL = "https://platform.ringcentral.com/restapi/oauth/token"
RINGCX_LOGIN_URL = "https://ringcx.ringcentral.com/api/auth/login/rc/accesstoken"

def read_rc_credentials_from_file(credentials_file="rc-credentials.json"):
    """
    Read RingCentral credentials from rc-credentials.json file.
    
    Args:
        credentials_file (str): Path to JSON file containing RingCentral credentials
        
    Returns:
        dict: RingCentral credentials
        
    Raises:
        RuntimeError: If file reading or parsing fails
    """
    try:
        with open(credentials_file, "r", encoding="utf-8") as file:
            credentials = json.load(file)
            
        required_fields = ["clientId", "clientSecret", "server"]
        missing_fields = [field for field in required_fields if field not in credentials]
        
        if missing_fields:
            raise RuntimeError(f"Missing required fields in {credentials_file}: {missing_fields}")
            
        return credentials
        
    except FileNotFoundError:
        raise RuntimeError(f"{credentials_file} not found")
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Invalid JSON in {credentials_file}: {str(e)}")
    except Exception as e:
        raise RuntimeError(f"Error reading {credentials_file}: {str(e)}")

def get_ringcentral_access_token_from_rc_credentials(credentials_file="rc-credentials.json"):
    """
    Get RingCentral access token using credentials from rc-credentials.json file.
    This uses the JWT token directly from the credentials file.
    
    Args:
        credentials_file (str): Path to rc-credentials.json file
        
    Returns:
        dict: Response containing access token and other authentication data
        
    Raises:
        RuntimeError: If authentication fails
    """
    try:
        # Read credentials from file
        credentials = read_rc_credentials_from_file(credentials_file)
        
        # Check if JWT token is available
        if "jwt" not in credentials or "timezero" not in credentials["jwt"]:
            raise RuntimeError("JWT token not found in credentials file")
        
        jwt_token = credentials["jwt"]["timezero"]
        server_url = credentials["server"]
        
        # Prepare the token endpoint URL
        token_url = f"{server_url}/restapi/oauth/token"
        
        # Prepare headers with basic authentication
        import base64
        client_credentials = f"{credentials['clientId']}:{credentials['clientSecret']}"
        encoded_credentials = base64.b64encode(client_credentials.encode()).decode()

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json",
            "Authorization": f"Basic {encoded_credentials}"
        }

        # Prepare payload for token exchange
        payload = {
            "grant_type": "urn:ietf:params:oauth:grant-type:jwt-bearer",
            "assertion": jwt_token
        }
        
        # Make the token request
        response = requests.post(token_url, headers=headers, data=payload, timeout=30)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"RingCentral authentication failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"RingCentral authentication error: {str(e)}")

def get_ringcx_access_token_with_rc_token(rc_access_token, login_url=None):
    """
    Get RingCX access token using RingCentral access token.
    
    Args:
        rc_access_token (str): RingCentral access token
        login_url (str, optional): RingCX login URL
        
    Returns:
        dict: Response containing RingCX access token
        
    Raises:
        RuntimeError: If authentication fails
    """
    url = login_url or RINGCX_LOGIN_URL
    
    # Prepare headers
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # Prepare payload
    payload = {
        "token": rc_access_token
    }
    
    try:
        # Make the POST request
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        # Check if request was successful
        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"RingCX authentication failed: {response.status_code} - {response.text}")
            
    except requests.exceptions.RequestException as e:
        raise RuntimeError(f"Network error during RingCX authentication: {str(e)}")
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Invalid JSON response from RingCX: {str(e)}")

def get_ringcx_access_token_from_rc_credentials(credentials_file="rc-credentials.json"):
    """
    Get RingCX access token using RingCentral credentials from rc-credentials.json file.
    This is the complete flow: RC credentials → RC access token → RingCX access token.
    
    Args:
        credentials_file (str): Path to rc-credentials.json file
        
    Returns:
        dict: Response containing RingCX access token
        
    Raises:
        RuntimeError: If authentication fails
    """
    try:
        # Step 1: Get RingCentral access token using JWT from credentials file
        rc_response = get_ringcentral_access_token_from_rc_credentials(credentials_file)
        
        # Step 2: Extract the RingCentral access token
        rc_access_token = rc_response.get("access_token")
        if not rc_access_token:
            raise RuntimeError("No access token in RingCentral response")
        
        # Step 3: Use the RingCentral access token to get RingCX access token
        return get_ringcx_access_token_with_rc_token(rc_access_token)
        
    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"RingCX authentication from RC credentials failed: {str(e)}")

def extract_access_token(auth_response):
    """
    Extract the access token from the authentication response.
    
    Args:
        auth_response (dict): Response from authentication functions
        
    Returns:
        str: Access token
        
    Raises:
        RuntimeError: If access token is not found in response
    """
    if not isinstance(auth_response, dict):
        raise RuntimeError("Invalid response format")
    
    # Common field names for access tokens
    token_fields = ["access_token", "accessToken", "token", "authToken"]
    
    for field in token_fields:
        if field in auth_response:
            return auth_response[field]
    
    # If not found in common fields, check if the entire response is the token
    if isinstance(auth_response, str):
        return auth_response
    
    raise RuntimeError(f"Access token not found in response. Available fields: {list(auth_response.keys())}")

def test_authentication():
    """
    Test the authentication functions with rc-credentials.json.
    """
    try:
        print("Testing RingCentral and RingCX Authentication")
        print("=" * 50)
        
        # Test 1: Read credentials
        print("\n1. Reading RC credentials...")
        credentials = read_rc_credentials_from_file()
        print("✓ Credentials loaded successfully")
        print(f"Client ID: {credentials['clientId']}")
        print(f"Server: {credentials['server']}")
        print(f"JWT available: {'jwt' in credentials and 'timezero' in credentials['jwt']}")
        
        # Test 2: Get RingCentral access token
        print("\n2. Getting RingCentral access token...")
        rc_response = get_ringcentral_access_token_from_rc_credentials()
        print("✓ RingCentral authentication successful")
        print(f"Response keys: {list(rc_response.keys())}")
        
        rc_access_token = extract_access_token(rc_response)
        print(f"RC access token preview: {rc_access_token[:50]}...")
        
        # Test 3: Get RingCX access token
        print("\n3. Getting RingCX access token...")
        ringcx_response = get_ringcx_access_token_from_rc_credentials()
        print("✓ RingCX authentication successful")
        print(f"Response keys: {list(ringcx_response.keys())}")
        
        ringcx_access_token = extract_access_token(ringcx_response)
        print(f"RingCX access token preview: {ringcx_access_token[:50]}...")
        
        print("\n" + "=" * 50)
        print("✓ All authentication tests passed!")
        
        return ringcx_access_token
        
    except Exception as e:
        print(f"\n✗ Authentication failed: {e}")
        return None

# Main execution
if __name__ == "__main__":
    print("RingCentral/RingCX Authentication Test")
    print("Using rc-credentials.json file")
    print("=" * 40)
    
    access_token = test_authentication()
    
    if access_token:
        print(f"\n✓ Successfully obtained RingCX access token")
        print(f"Token length: {len(access_token)} characters")
        print(f"Use this token for RingCX API calls")
    else:
        print(f"\n✗ Failed to obtain access token")
        print("Check your rc-credentials.json file and network connection")
