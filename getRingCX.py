import requests
import json
import jwt
import time
from datetime import datetime, timedelta, timezone

LOGIN_URL = "https://ringcx.ringcentral.com/api/auth/login/rc/accesstoken"
JWT_LOGIN_URL = "https://ringcx.ringcentral.com/api/auth/login/rc/jwt"
RINGCENTRAL_TOKEN_URL = "https://platform.ringcentral.com/restapi/oauth/token"

def get_rc_access_token_from_file(token_file="RC_ACCESS_TOKEN.txt"):
    """
    Read RC_ACCESS_TOKEN from file.

    Args:
        token_file (str): Path to file containing RC_ACCESS_TOKEN

    Returns:
        str: RC_ACCESS_TOKEN from file

    Raises:
        RuntimeError: If file reading fails
    """
    try:
        with open(token_file, "r", encoding="utf-8") as file:
            rc_token = file.read().strip()

        if not rc_token:
            raise RuntimeError(f"{token_file} is empty")

        return rc_token

    except FileNotFoundError:
        raise RuntimeError(f"{token_file} not found")
    except Exception as e:
        raise RuntimeError(f"Error reading {token_file}: {str(e)}")

def get_ringcx_access_token(rc_access_token=None, login_url=None):
    """
    Get RingCX access token using the RC_ACCESS_TOKEN.

    Args:
        rc_access_token (str, optional): RingCentral access token. If None, reads from file
        login_url (str, optional): Login URL. If None, uses global LOGIN_URL

    Returns:
        dict: Response containing access token and other authentication data

    Raises:
        RuntimeError: If the authentication request fails
    """
    # Get token from parameter or file
    if rc_access_token is None:
        token = get_rc_access_token_from_file()
    else:
        token = rc_access_token

    # Get URL
    url = login_url or LOGIN_URL

    if not token:
        raise RuntimeError("RC_ACCESS_TOKEN is required")

    if not url:
        raise RuntimeError("Login URL is required")

    # Prepare headers
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # Prepare payload
    payload = {
        "token": token
    }

    try:
        # Make the POST request
        response = requests.post(url, headers=headers, json=payload, timeout=30)

        # Check if request was successful
        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"Authentication failed: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        raise RuntimeError(f"Network error during authentication: {str(e)}")
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Invalid JSON response: {str(e)}")

def get_ringcx_token_from_file(token_file="RC_ACCESS_TOKEN.txt", login_url=None):
    """
    Get RingCX access token by reading RC_ACCESS_TOKEN from a file.

    Args:
        token_file (str): Path to file containing RC_ACCESS_TOKEN
        login_url (str, optional): Login URL. If None, uses global LOGIN_URL

    Returns:
        dict: Response containing access token and other authentication data

    Raises:
        RuntimeError: If file reading or authentication fails
    """
    try:
        # Read the RC token from file
        rc_token = get_rc_access_token_from_file(token_file)

        # Use the token to get RingCX access token
        return get_ringcx_access_token(rc_token, login_url)

    except Exception as e:
        raise RuntimeError(f"Error getting token from file: {str(e)}")

def extract_access_token(auth_response):
    """
    Extract the access token from the authentication response.

    Args:
        auth_response (dict): Response from get_ringcx_access_token()

    Returns:
        str: Access token

    Raises:
        RuntimeError: If access token is not found in response
    """
    if not isinstance(auth_response, dict):
        raise RuntimeError("Invalid response format")

    # Common field names for access tokens
    token_fields = ["access_token", "accessToken", "token", "authToken"]

    for field in token_fields:
        if field in auth_response:
            return auth_response[field]

    # If not found in common fields, check if the entire response is the token
    if isinstance(auth_response, str):
        return auth_response

    raise RuntimeError(f"Access token not found in response. Available fields: {list(auth_response.keys())}")

def read_jwt_credentials_from_file(credentials_file="JWT_CREDENTIALS.json"):
    """
    Read JWT credentials from a JSON file.

    Args:
        credentials_file (str): Path to JSON file containing JWT credentials

    Returns:
        dict: JWT credentials containing client_id, client_secret, jwt_token, etc.

    Raises:
        RuntimeError: If file reading or parsing fails
    """
    try:
        with open(credentials_file, "r", encoding="utf-8") as file:
            credentials = json.load(file)

        required_fields = ["client_id", "client_secret", "jwt_private_key"]
        missing_fields = [field for field in required_fields if field not in credentials]

        if missing_fields:
            raise RuntimeError(f"Missing required fields in {credentials_file}: {missing_fields}")

        return credentials

    except FileNotFoundError:
        raise RuntimeError(f"{credentials_file} not found")
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Invalid JSON in {credentials_file}: {str(e)}")
    except Exception as e:
        raise RuntimeError(f"Error reading {credentials_file}: {str(e)}")

def create_jwt_token(client_id, client_secret, jwt_private_key, server_url=None):
    """
    Create a JWT token for RingCentral authentication.

    Args:
        client_id (str): RingCentral application client ID
        client_secret (str): RingCentral application client secret
        jwt_private_key (str): Private key for JWT signing
        server_url (str, optional): RingCentral server URL

    Returns:
        str: JWT token

    Raises:
        RuntimeError: If JWT creation fails
    """
    try:
        # Set default server URL if not provided
        if not server_url:
            server_url = "https://platform.ringcentral.com"

        # Create JWT payload
        now = datetime.now(timezone.utc)
        payload = {
            "iss": client_id,  # Issuer (client ID)
            "sub": client_id,  # Subject (client ID)
            "aud": server_url,  # Audience (RingCentral server)
            "iat": int(now.timestamp()),  # Issued at
            "exp": int((now + timedelta(minutes=5)).timestamp()),  # Expires in 5 minutes
            "jti": f"{client_id}-{int(now.timestamp())}"  # JWT ID
        }

        # Create JWT token
        jwt_token = jwt.encode(payload, jwt_private_key, algorithm="RS256")

        return jwt_token

    except Exception as e:
        raise RuntimeError(f"Failed to create JWT token: {str(e)}")

def get_ringcentral_access_token_with_jwt(client_id, client_secret, jwt_private_key, server_url=None):
    """
    Get RingCentral access token using JWT authentication.

    Args:
        client_id (str): RingCentral application client ID
        client_secret (str): RingCentral application client secret
        jwt_private_key (str): Private key for JWT signing
        server_url (str, optional): RingCentral server URL

    Returns:
        dict: Response containing access token and other authentication data

    Raises:
        RuntimeError: If authentication fails
    """
    try:
        # Create JWT token
        jwt_token = create_jwt_token(client_id, client_secret, jwt_private_key, server_url)

        # Set token URL
        token_url = f"{server_url or 'https://platform.ringcentral.com'}/restapi/oauth/token"

        # Prepare headers
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json"
        }

        # Prepare payload for token exchange
        payload = {
            "grant_type": "urn:ietf:params:oauth:grant-type:jwt-bearer",
            "assertion": jwt_token
        }

        # Make the token request
        response = requests.post(token_url, headers=headers, data=payload, timeout=30)

        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"JWT authentication failed: {response.status_code} - {response.text}")

    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"JWT authentication error: {str(e)}")

def get_ringcx_access_token_with_jwt(jwt_credentials=None, credentials_file="JWT_CREDENTIALS.json"):
    """
    Get RingCX access token using JWT authentication.

    Args:
        jwt_credentials (dict, optional): JWT credentials. If None, reads from file
        credentials_file (str): Path to credentials file

    Returns:
        dict: Response containing RingCX access token

    Raises:
        RuntimeError: If authentication fails
    """
    try:
        # Get credentials
        if jwt_credentials is None:
            credentials = read_jwt_credentials_from_file(credentials_file)
        else:
            credentials = jwt_credentials

        # First, get RingCentral access token using JWT
        rc_access_token_response = get_ringcentral_access_token_with_jwt(
            credentials["client_id"],
            credentials["client_secret"],
            credentials["jwt_private_key"],
            credentials.get("server_url")
        )

        # Extract the access token
        rc_access_token = rc_access_token_response.get("access_token")
        if not rc_access_token:
            raise RuntimeError("No access token in RingCentral response")

        # Now use this token to get RingCX access token
        return get_ringcx_access_token(rc_access_token, JWT_LOGIN_URL)

    except Exception as e:
        if isinstance(e, RuntimeError):
            raise
        raise RuntimeError(f"RingCX JWT authentication failed: {str(e)}")

# Example usage function
def example_usage():
    """
    Example of how to use the authentication functions.
    """
    try:
        print("Getting RingCX access token...")

        # Method 1: Using RC_ACCESS_TOKEN from file
        print("\n1. Trying RC_ACCESS_TOKEN authentication...")
        try:
            auth_response = get_ringcx_access_token()
            print("✓ RC_ACCESS_TOKEN authentication successful")
            print(f"Response keys: {list(auth_response.keys())}")

            # Extract access token
            access_token = extract_access_token(auth_response)
            print(f"✓ Access token extracted: {access_token[:50]}...")
            return access_token
        except Exception as e:
            print(f"✗ RC_ACCESS_TOKEN authentication failed: {e}")

        # Method 2: Using JWT authentication
        print("\n2. Trying JWT authentication...")
        try:
            auth_response = get_ringcx_access_token_with_jwt()
            print("✓ JWT authentication successful")
            print(f"Response keys: {list(auth_response.keys())}")

            # Extract access token
            access_token = extract_access_token(auth_response)
            print(f"✓ Access token extracted: {access_token[:50]}...")
            return access_token
        except Exception as e:
            print(f"✗ JWT authentication failed: {e}")

        print("\n✗ All authentication methods failed")
        return None

    except RuntimeError as e:
        print(f"✗ Error: {e}")
        return None

# Main execution
if __name__ == "__main__":
    print("RingCX Access Token Retrieval")
    print("=" * 40)

    # Run the example
    access_token = example_usage()

    if access_token:
        print(f"\n✓ Successfully obtained access token")
        print(f"Token length: {len(access_token)} characters")
    else:
        print(f"\n✗ Failed to obtain access token")

