import requests
import json

LOGIN_URL = "https://ringcx.ringcentral.com/api/auth/login/rc/accesstoken"

def get_rc_access_token_from_file(token_file="RC_ACCESS_TOKEN.txt"):
    """
    Read RC_ACCESS_TOKEN from file.

    Args:
        token_file (str): Path to file containing RC_ACCESS_TOKEN

    Returns:
        str: RC_ACCESS_TOKEN from file

    Raises:
        RuntimeError: If file reading fails
    """
    try:
        with open(token_file, "r", encoding="utf-8") as file:
            rc_token = file.read().strip()

        if not rc_token:
            raise RuntimeError(f"{token_file} is empty")

        return rc_token

    except FileNotFoundError:
        raise RuntimeError(f"{token_file} not found")
    except Exception as e:
        raise RuntimeError(f"Error reading {token_file}: {str(e)}")

def get_ringcx_access_token(rc_access_token=None, login_url=None):
    """
    Get RingCX access token using the RC_ACCESS_TOKEN.

    Args:
        rc_access_token (str, optional): RingCentral access token. If None, reads from file
        login_url (str, optional): Login URL. If None, uses global LOGIN_URL

    Returns:
        dict: Response containing access token and other authentication data

    Raises:
        RuntimeError: If the authentication request fails
    """
    # Get token from parameter or file
    if rc_access_token is None:
        token = get_rc_access_token_from_file()
    else:
        token = rc_access_token

    # Get URL
    url = login_url or LOGIN_URL

    if not token:
        raise RuntimeError("RC_ACCESS_TOKEN is required")

    if not url:
        raise RuntimeError("Login URL is required")

    # Prepare headers
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # Prepare payload
    payload = {
        "token": token
    }

    try:
        # Make the POST request
        response = requests.post(url, headers=headers, json=payload, timeout=30)

        # Check if request was successful
        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"Authentication failed: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        raise RuntimeError(f"Network error during authentication: {str(e)}")
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Invalid JSON response: {str(e)}")

def get_ringcx_token_from_file(token_file="RC_ACCESS_TOKEN.txt", login_url=None):
    """
    Get RingCX access token by reading RC_ACCESS_TOKEN from a file.

    Args:
        token_file (str): Path to file containing RC_ACCESS_TOKEN
        login_url (str, optional): Login URL. If None, uses global LOGIN_URL

    Returns:
        dict: Response containing access token and other authentication data

    Raises:
        RuntimeError: If file reading or authentication fails
    """
    try:
        # Read the RC token from file
        rc_token = get_rc_access_token_from_file(token_file)

        # Use the token to get RingCX access token
        return get_ringcx_access_token(rc_token, login_url)

    except Exception as e:
        raise RuntimeError(f"Error getting token from file: {str(e)}")

def extract_access_token(auth_response):
    """
    Extract the access token from the authentication response.

    Args:
        auth_response (dict): Response from get_ringcx_access_token()

    Returns:
        str: Access token

    Raises:
        RuntimeError: If access token is not found in response
    """
    if not isinstance(auth_response, dict):
        raise RuntimeError("Invalid response format")

    # Common field names for access tokens
    token_fields = ["access_token", "accessToken", "token", "authToken"]

    for field in token_fields:
        if field in auth_response:
            return auth_response[field]

    # If not found in common fields, check if the entire response is the token
    if isinstance(auth_response, str):
        return auth_response

    raise RuntimeError(f"Access token not found in response. Available fields: {list(auth_response.keys())}")

# Example usage function
def example_usage():
    """
    Example of how to use the authentication functions.
    """
    try:
        print("Getting RingCX access token...")

        # Method 1: Using hardcoded token
        auth_response = get_ringcx_access_token()
        print("✓ Authentication successful")
        print(f"Response keys: {list(auth_response.keys())}")

        # Extract access token
        access_token = extract_access_token(auth_response)
        print(f"✓ Access token extracted: {access_token[:50]}...")

        # Method 2: Using token from file (uncomment to use)
        # auth_response = get_ringcx_token_from_file("RC_ACCESS_TOKEN.txt")
        # access_token = extract_access_token(auth_response)

        return access_token

    except RuntimeError as e:
        print(f"✗ Error: {e}")
        return None

# Main execution
if __name__ == "__main__":
    print("RingCX Access Token Retrieval")
    print("=" * 40)

    # Run the example
    access_token = example_usage()

    if access_token:
        print(f"\n✓ Successfully obtained access token")
        print(f"Token length: {len(access_token)} characters")
    else:
        print(f"\n✗ Failed to obtain access token")

