# RingCX Access Token Functions

This module provides functions to authenticate with RingCX and obtain access tokens using the RC_ACCESS_TOKEN.

## Files

- `getRingCX.py` - Main module with authentication functions
- `test_ringcx.py` - Test script to verify functionality
- `RC_ACCESS_TOKEN.txt` - File containing the RC_ACCESS_TOKEN
- `RingCX_README.md` - This documentation file

## Functions

### `get_ringcx_access_token(rc_access_token=None, login_url=None)`

Main function to get RingCX access token using the RC_ACCESS_TOKEN.

**Parameters:**
- `rc_access_token` (str, optional): RingCentral access token. If None, uses global RC_ACCESS_TOKEN
- `login_url` (str, optional): Login URL. If None, uses global LOGIN_URL

**Returns:**
- `dict`: Response containing access token and other authentication data

**Raises:**
- `RuntimeError`: If the authentication request fails

**Example:**
```python
from getRingCX import get_ringcx_access_token

# Using default hardcoded token
auth_response = get_ringcx_access_token()

# Using custom token
auth_response = get_ringcx_access_token("your_custom_token")
```

### `get_ringcx_token_from_file(token_file="RC_ACCESS_TOKEN.txt", login_url=None)`

Get RingCX access token by reading RC_ACCESS_TOKEN from a file.

**Parameters:**
- `token_file` (str): Path to file containing RC_ACCESS_TOKEN
- `login_url` (str, optional): Login URL. If None, uses global LOGIN_URL

**Returns:**
- `dict`: Response containing access token and other authentication data

**Raises:**
- `RuntimeError`: If file reading or authentication fails

**Example:**
```python
from getRingCX import get_ringcx_token_from_file

# Using default file
auth_response = get_ringcx_token_from_file()

# Using custom file
auth_response = get_ringcx_token_from_file("my_token.txt")
```

### `extract_access_token(auth_response)`

Extract the access token from the authentication response.

**Parameters:**
- `auth_response` (dict): Response from get_ringcx_access_token()

**Returns:**
- `str`: Access token

**Raises:**
- `RuntimeError`: If access token is not found in response

**Example:**
```python
from getRingCX import get_ringcx_access_token, extract_access_token

auth_response = get_ringcx_access_token()
access_token = extract_access_token(auth_response)
print(f"Access token: {access_token}")
```

## Complete Usage Example

```python
from getRingCX import get_ringcx_access_token, extract_access_token

try:
    # Get authentication response
    auth_response = get_ringcx_access_token()
    
    # Extract the access token
    access_token = extract_access_token(auth_response)
    
    print(f"Successfully obtained access token: {access_token[:50]}...")
    
    # Use the access token for API calls
    # headers = {"Authorization": f"Bearer {access_token}"}
    
except RuntimeError as e:
    print(f"Authentication failed: {e}")
```

## Configuration

### Hardcoded Configuration (Default)
The module includes hardcoded values:
- `RC_ACCESS_TOKEN`: Your RingCentral access token
- `LOGIN_URL`: RingCX authentication endpoint

### File-based Configuration
Create `RC_ACCESS_TOKEN.txt` with your token:
```
your_rc_access_token_here
```

## Testing

Run the test script to verify functionality:

```bash
# Basic test
python test_ringcx.py

# Create sample token file and test
python test_ringcx.py --create-file

# Run the main module directly
python getRingCX.py
```

## Error Handling

All functions raise `RuntimeError` with descriptive messages for:
- Missing or invalid tokens
- Network errors
- Authentication failures
- File reading errors
- Invalid response formats

## API Endpoint

The module uses the RingCX authentication endpoint:
```
https://ringcx.ringcentral.com/api/auth/login/rc/accesstoken
```

**Request Format:**
```json
{
    "token": "your_rc_access_token"
}
```

**Headers:**
```json
{
    "Content-Type": "application/json",
    "Accept": "application/json"
}
```

## Security Notes

- Keep your RC_ACCESS_TOKEN secure
- Don't commit tokens to version control
- Use file-based configuration for production
- Tokens may have expiration dates
- Handle authentication errors gracefully

## Troubleshooting

1. **Authentication Failed**: Check if your RC_ACCESS_TOKEN is valid and not expired
2. **Network Error**: Verify internet connection and endpoint availability
3. **File Not Found**: Ensure RC_ACCESS_TOKEN.txt exists and is readable
4. **Invalid Response**: Check if the API endpoint format has changed

## Dependencies

- `requests`: For HTTP requests
- `json`: For JSON parsing (built-in)
