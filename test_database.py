#!/usr/bin/env python3
"""
Simple test script to verify the database functionality.
Run this to test database operations without processing actual calls.
"""

import sqlite3
import os
from datetime import datetime

def test_database():
    """Test the database functionality with sample data."""
    
    # Import the database functions from the main script
    import sys
    sys.path.append('.')
    
    try:
        from test import init_database, save_to_database, view_database_records
        
        print("Testing database functionality...")
        
        # Test 1: Initialize database
        print("\n1. Initializing database...")
        db_path = init_database()
        if db_path:
            print(f"✓ Database initialized: {db_path}")
        else:
            print("✗ Failed to initialize database")
            return
        
        # Test 2: Save sample data
        print("\n2. Saving sample data...")
        sample_results = [
            ("<PERSON>", "Customer called about billing issue. Resolved successfully.", 0.75, "Summary: Customer satisfaction was high. The agent handled the billing inquiry professionally and resolved the issue quickly. Rating: 9/10 - Excellent service."),
            ("<PERSON>", "Technical support call. Issue escalated.", -0.25, "Summary: Customer was frustrated with technical issues. Agent attempted troubleshooting but had to escalate. Rating: 6/10 - Needs improvement in technical resolution.")
        ]
        
        success = save_to_database(sample_results)
        if success:
            print("✓ Sample data saved successfully")
        else:
            print("✗ Failed to save sample data")
            return
        
        # Test 3: View records
        print("\n3. Viewing saved records...")
        records = view_database_records(5)
        if records:
            print("✓ Records retrieved successfully")
        else:
            print("✗ No records found or error retrieving records")
        
        # Test 4: Check database file
        print("\n4. Checking database file...")
        if os.path.exists("call_analysis.db"):
            file_size = os.path.getsize("call_analysis.db")
            print(f"✓ Database file exists, size: {file_size} bytes")
        else:
            print("✗ Database file not found")
        
        print("\n✓ Database test completed successfully!")
        
    except ImportError as e:
        print(f"✗ Error importing functions: {e}")
    except Exception as e:
        print(f"✗ Error during testing: {e}")

if __name__ == "__main__":
    test_database()
